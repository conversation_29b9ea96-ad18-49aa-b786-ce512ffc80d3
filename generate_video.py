#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片序列生成视频工具
将指定目录下的图片按序号生成视频文件
"""

import cv2
import os
import glob
import re
import sys

def images_to_video(input_dir, output_video_path, fps=15, image_extensions=None):
    """
    将目录下的图片按序号生成视频
    :param input_dir: 输入图片目录路径
    :param output_video_path: 输出视频文件路径
    :param fps: 视频帧率，默认15帧
    :param image_extensions: 支持的图片格式列表，默认为常见格式
    :return: 是否成功生成视频
    """
    if image_extensions is None:
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    
    try:
        # 检查输入目录是否存在
        if not os.path.exists(input_dir):
            print(f"错误: 输入目录不存在: {input_dir}")
            return False
        
        # 获取所有图片文件
        image_files = []
        for ext in image_extensions:
            pattern = os.path.join(input_dir, f"*{ext}")
            image_files.extend(glob.glob(pattern))
            pattern = os.path.join(input_dir, f"*{ext.upper()}")
            image_files.extend(glob.glob(pattern))
        
        if not image_files:
            print(f"错误: 在目录 {input_dir} 中未找到任何图片文件")
            print(f"支持的格式: {image_extensions}")
            return False
        
        # 按文件名中的数字排序
        def extract_number(filename):
            """从文件名中提取数字用于排序"""
            basename = os.path.basename(filename)
            numbers = re.findall(r'\d+', basename)
            return int(numbers[0]) if numbers else 0
        
        image_files.sort(key=extract_number)
        
        print(f"找到 {len(image_files)} 个图片文件")
        print(f"第一个文件: {os.path.basename(image_files[0])}")
        print(f"最后一个文件: {os.path.basename(image_files[-1])}")
        
        # 读取第一张图片获取尺寸
        first_image = cv2.imread(image_files[0])
        if first_image is None:
            print(f"错误: 无法读取第一张图片: {image_files[0]}")
            return False
        
        height, width = first_image.shape[:2]
        print(f"图片尺寸: {width} x {height}")
        
        # 创建视频编码器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # 使用mp4v编码
        video_writer = cv2.VideoWriter(output_video_path, fourcc, fps, (width, height))
        
        if not video_writer.isOpened():
            print("错误: 无法创建视频编码器")
            return False
        
        # 逐帧写入视频
        print(f"开始生成视频，帧率: {fps} FPS")
        for i, image_path in enumerate(image_files):
            # 读取图片
            img = cv2.imread(image_path)
            if img is None:
                print(f"警告: 无法读取图片 {image_path}，跳过")
                continue
            
            # 检查图片尺寸是否一致
            if img.shape[:2] != (height, width):
                print(f"警告: 图片 {os.path.basename(image_path)} 尺寸不一致，进行缩放")
                img = cv2.resize(img, (width, height))
            
            # 写入视频帧
            video_writer.write(img)
            
            # 显示进度
            if (i + 1) % 10 == 0 or i == len(image_files) - 1:
                print(f"进度: {i + 1}/{len(image_files)} ({(i + 1) / len(image_files) * 100:.1f}%)")
        
        # 释放资源
        video_writer.release()
        
        print(f"视频生成成功!")
        print(f"输出文件: {output_video_path}")
        print(f"总帧数: {len(image_files)}")
        print(f"视频时长: {len(image_files) / fps:.2f} 秒")
        
        return True
        
    except Exception as e:
        print(f"生成视频时发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 配置参数
    input_directory = r"D:\img\rgb"  # 输入图片目录
    output_video_file = "rgb_sequence_video.mp4"  # 输出视频文件名
    frame_rate = 15  # 帧率
    
    print("=" * 60)
    print("图片序列生成视频工具")
    print("=" * 60)
    print(f"输入目录: {input_directory}")
    print(f"输出视频: {output_video_file}")
    print(f"帧率: {frame_rate} FPS")
    print("=" * 60)
    
    # 生成视频
    success = images_to_video(input_directory, output_video_file, frame_rate)
    
    if success:
        print("\n✓ 视频生成成功!")
        if os.path.exists(output_video_file):
            file_size = os.path.getsize(output_video_file) / (1024 * 1024)  # MB
            print(f"文件大小: {file_size:.2f} MB")
    else:
        print("\n✗ 视频生成失败!")
        sys.exit(1)
