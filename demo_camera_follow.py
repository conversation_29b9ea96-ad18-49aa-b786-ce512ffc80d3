"""
演示Camera跟随投影仪旋转的效果
"""
import cv2
import numpy as np
import math
import time

def create_demo_image():
    """创建一个演示用的图像"""
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加网格
    for i in range(0, 640, 80):
        cv2.line(img, (i, 0), (i, 480), (50, 50, 50), 1)
    for i in range(0, 480, 60):
        cv2.line(img, (0, i), (640, i), (50, 50, 50), 1)
    
    # 添加方向指示箭头
    cv2.arrowedLine(img, (320, 240), (420, 240), (0, 255, 0), 3, tipLength=0.1)
    cv2.putText(img, "X+", (430, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
    
    cv2.arrowedLine(img, (320, 240), (320, 140), (255, 0, 0), 3, tipLength=0.1)
    cv2.putText(img, "Y+", (325, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
    
    # 添加中心标记
    cv2.circle(img, (320, 240), 10, (255, 255, 255), -1)
    cv2.putText(img, "CENTER", (270, 270), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    # 添加角点标记
    corners = [(50, 50), (590, 50), (590, 430), (50, 430)]
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
    labels = ["TL", "TR", "BR", "BL"]
    
    for corner, color, label in zip(corners, colors, labels):
        cv2.circle(img, corner, 8, color, -1)
        cv2.putText(img, label, (corner[0]-15, corner[1]-15), cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)
    
    return img

def main():
    print("Camera Follow Projector Demo")
    print("This demo shows how the camera view changes when following projector rotation")
    print("Press any key to continue...")
    
    # 创建演示图像
    demo_img = create_demo_image()
    cv2.imwrite("demo_pattern.png", demo_img)
    
    # 显示演示图像
    cv2.namedWindow("Demo Pattern", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Demo Pattern", 640, 480)
    cv2.imshow("Demo Pattern", demo_img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    print("\nDemo pattern created and saved as 'demo_pattern.png'")
    print("\nTo test the camera follow effect:")
    print("1. Run: python cz_projector_simulate_keyboard.py")
    print("2. Press 'C' to toggle between 'Following' and 'Independent' modes")
    print("3. Use W/S/A/D/Z/X to rotate the projector and observe:")
    print("   - In 'Following' mode: Camera rotates with projector")
    print("   - In 'Independent' mode: Camera rotation is separate")
    print("4. Use T/G/F/H/R/Y to adjust camera's relative angles")
    print("\nKey differences:")
    print("- Following mode: Camera acts as if mounted on projector")
    print("- Independent mode: Camera has its own coordinate system")

if __name__ == "__main__":
    main()
