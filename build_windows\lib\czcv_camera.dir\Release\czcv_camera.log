﻿  center_stage_api.cpp
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_capi.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(97,11): warning C4251: “czcv_camera::Any::m_ptr”: class“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(67): message : 参见“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(98,19): warning C4251: “czcv_camera::Any::m_tpIndex”: class“std::type_index”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\typeindex(25): message : 参见“std::type_index”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(117,14): warning C4251: “czcv_camera::DynamicParams::_mu”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(168,31): warning C4251: “czcv_camera::DynamicParams::_keyValues”: class“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(162): message : 参见“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base\status.h(46,21): warning C4251: “czcv_camera::Status::message_”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Status”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base\common.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base\common.h(56,11): warning C4251: “czcv_camera::ImageBlob::frameCopiedBGR”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::ImageBlob”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(39,21): warning C4251: “czcv_camera::AbstarctModel::_profileData”: class“czcv_camera::ProfileData”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base\profile_data.h(9): message : 参见“czcv_camera::ProfileData”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(41,34): warning C4251: “czcv_camera::AbstarctModel::_modelConfig”: class“std::vector<cv::String,std::allocator<cv::String>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/persistence.hpp(417): message : 参见“std::vector<cv::String,std::allocator<cv::String>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(45,21): warning C4251: “czcv_camera::AbstarctModel::_modelName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(24,17): warning C4251: “czcv_camera::DetInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30,29): warning C4251: “czcv_camera::DetInputOutput::_bbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(31,49): warning C4251: “czcv_camera::DetInputOutput::_landmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32,49): warning C4251: “czcv_camera::DetInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(59,17): warning C4251: “czcv_camera::TrackerInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(60,29): warning C4251: “czcv_camera::TrackerInputOutput::_inBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(67,29): warning C4251: “czcv_camera::TrackerInputOutput::_trackedBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(68,49): warning C4251: “czcv_camera::TrackerInputOutput::_trackedLandmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(69,42): warning C4251: “czcv_camera::TrackerInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74,41): warning C4251: “czcv_camera::TrackerInputOutput::_gestureResults”: class“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74): message : 参见“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(261,45): warning C4251: “czcv_camera::BaseTracker::_detector”: class“std::shared_ptr<czcv_camera::BaseObjectDetector>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(241): message : 参见“std::shared_ptr<czcv_camera::BaseObjectDetector>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(268,19): warning C4251: “czcv_camera::BaseTracker::_lastRoi”: class“cv::Rect_<int>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/types.hpp(459): message : 参见“cv::Rect_<int>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_api.h(40,21): warning C4251: “czcv_camera::Android_API::_modeldir”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Android_API”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_api.h(46,20): warning C4251: “czcv_camera::Android_API::_muMode”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Android_API”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(107,20): warning C4251: “czcv_camera::PoolAllocator::budgets_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(108,20): warning C4251: “czcv_camera::PoolAllocator::payouts_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110,46): warning C4251: “czcv_camera::PoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(111,46): warning C4251: “czcv_camera::PoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(132,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(133,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(57,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapDtoS”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(58,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapStoD”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(67,5): warning C4275: 非 dll 接口 class“czcv_camera::Abstarct_PersonViewer_DataCallback”用作 dll 接口 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的基
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(24): message : 参见“czcv_camera::Abstarct_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(66): message : 参见“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(140,17): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(146,33): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_cv”: class“std::condition_variable”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(583): message : 参见“std::condition_variable”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(147,20): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_mtx”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(212,42): warning C4251: “czcv_camera::Base_PersonViewer::_camDewarperPtr”: class“std::shared_ptr<czcv_camera::BaseCamDewarper>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(165): message : 参见“std::shared_ptr<czcv_camera::BaseCamDewarper>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(213,62): warning C4251: “czcv_camera::Base_PersonViewer::_dataCallbackPtr”: class“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(171): message : 参见“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(214,42): warning C4251: “czcv_camera::Base_PersonViewer::_rgaInterfacePtr”: class“std::shared_ptr<czcv_camera::rga_interface_t>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(27): message : 参见“std::shared_ptr<czcv_camera::rga_interface_t>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(217,37): warning C4251: “czcv_camera::Base_PersonViewer::_infos”: class“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(217): message : 参见“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(226,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapx”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(227,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapy”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(83,49): warning C4251: “czcv_camera::PersonCenterStager::_impl”: class“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”需要有 dll 接口由 class“czcv_camera::PersonCenterStager”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(83): message : 参见“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”的声明
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(170,37): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(170,32): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(170,27): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(170,22): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(172,26): warning C4244: “=”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(173,26): warning C4244: “=”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(174,26): warning C4244: “=”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\center_stage_api.cpp(175,26): warning C4244: “=”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
  czcv_center_stage.cpp
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(56,11): warning C4251: “czcv_camera::ImageBlob::frameCopiedBGR”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::ImageBlob”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(97,11): warning C4251: “czcv_camera::Any::m_ptr”: class“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(67): message : 参见“std::unique_ptr<czcv_camera::Any::Base,std::default_delete<czcv_camera::Any::Base>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(98,19): warning C4251: “czcv_camera::Any::m_tpIndex”: class“std::type_index”需要有 dll 接口由 class“czcv_camera::Any”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\typeindex(25): message : 参见“std::type_index”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(117,14): warning C4251: “czcv_camera::DynamicParams::_mu”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(168,31): warning C4251: “czcv_camera::DynamicParams::_keyValues”: class“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”需要有 dll 接口由 class“czcv_camera::DynamicParams”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/dynamic_param.h(162): message : 参见“std::map<std::string,czcv_camera::Any,std::less<cv::String>,std::allocator<std::pair<const std::string,czcv_camera::Any>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/status.h(46,21): warning C4251: “czcv_camera::Status::message_”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Status”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/center_stage_capi.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(39,21): warning C4251: “czcv_camera::AbstarctModel::_profileData”: class“czcv_camera::ProfileData”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base\profile_data.h(9): message : 参见“czcv_camera::ProfileData”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(41,34): warning C4251: “czcv_camera::AbstarctModel::_modelConfig”: class“std::vector<cv::String,std::allocator<cv::String>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/persistence.hpp(417): message : 参见“std::vector<cv::String,std::allocator<cv::String>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(45,21): warning C4251: “czcv_camera::AbstarctModel::_modelName”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::AbstarctModel”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(24,17): warning C4251: “czcv_camera::DetInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30,29): warning C4251: “czcv_camera::DetInputOutput::_bbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(31,49): warning C4251: “czcv_camera::DetInputOutput::_landmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32,49): warning C4251: “czcv_camera::DetInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::DetInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(59,17): warning C4251: “czcv_camera::TrackerInputOutput::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(60,29): warning C4251: “czcv_camera::TrackerInputOutput::_inBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(67,29): warning C4251: “czcv_camera::TrackerInputOutput::_trackedBbox”: class“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(30): message : 参见“std::vector<czcv_camera::BboxF,std::allocator<czcv_camera::BboxF>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(68,49): warning C4251: “czcv_camera::TrackerInputOutput::_trackedLandmarks”: class“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/imgproc.hpp(1127): message : 参见“std::vector<std::vector<cv::Point2f,std::allocator<cv::Point2f>>,std::allocator<std::vector<cv::Point2f,std::allocator<cv::Point2f>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(69,42): warning C4251: “czcv_camera::TrackerInputOutput::_pose”: class“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(32): message : 参见“std::vector<std::vector<float,std::allocator<float>>,std::allocator<std::vector<float,std::allocator<float>>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74,41): warning C4251: “czcv_camera::TrackerInputOutput::_gestureResults”: class“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”需要有 dll 接口由 class“czcv_camera::TrackerInputOutput”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(74): message : 参见“std::vector<czcv_camera::stGestureRecResult,std::allocator<czcv_camera::stGestureRecResult>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(261,45): warning C4251: “czcv_camera::BaseTracker::_detector”: class“std::shared_ptr<czcv_camera::BaseObjectDetector>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/detector_factory.h(65): message : 参见“std::shared_ptr<czcv_camera::BaseObjectDetector>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\tracker/base_tracker.h(268,19): warning C4251: “czcv_camera::BaseTracker::_lastRoi”: class“cv::Rect_<int>”需要有 dll 接口由 class“czcv_camera::BaseTracker”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/types.hpp(459): message : 参见“cv::Rect_<int>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(107,20): warning C4251: “czcv_camera::PoolAllocator::budgets_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(108,20): warning C4251: “czcv_camera::PoolAllocator::payouts_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110,46): warning C4251: “czcv_camera::PoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(111,46): warning C4251: “czcv_camera::PoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(132,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(133,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(57,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapDtoS”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(58,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapStoD”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(67,5): warning C4275: 非 dll 接口 class“czcv_camera::Abstarct_PersonViewer_DataCallback”用作 dll 接口 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的基
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(24): message : 参见“czcv_camera::Abstarct_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(66): message : 参见“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(140,17): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(146,33): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_cv”: class“std::condition_variable”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(583): message : 参见“std::condition_variable”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(147,20): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_mtx”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(212,42): warning C4251: “czcv_camera::Base_PersonViewer::_camDewarperPtr”: class“std::shared_ptr<czcv_camera::BaseCamDewarper>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(165): message : 参见“std::shared_ptr<czcv_camera::BaseCamDewarper>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(213,62): warning C4251: “czcv_camera::Base_PersonViewer::_dataCallbackPtr”: class“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(171): message : 参见“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(214,42): warning C4251: “czcv_camera::Base_PersonViewer::_rgaInterfacePtr”: class“std::shared_ptr<czcv_camera::rga_interface_t>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/abstract_model.h(27): message : 参见“std::shared_ptr<czcv_camera::rga_interface_t>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(217,37): warning C4251: “czcv_camera::Base_PersonViewer::_infos”: class“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(217): message : 参见“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(226,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapx”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\person_viewer.h(227,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapy”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(83,49): warning C4251: “czcv_camera::PersonCenterStager::_impl”: class“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”需要有 dll 接口由 class“czcv_camera::PersonCenterStager”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/czcv_center_stage.h(83): message : 参见“std::shared_ptr<czcv_camera::PersonCenterStagerImpl>”的声明
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\../utils/async_runner.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\config/config_setter.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\tracker/person_assert/rknn_person_assert.h(80,26): warning C4305: “初始化”: 从“double”到“float”截断
D:\Program\Project\project\czcv_camera_new\lib\src\detector/detect_white_board.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  cl_version.h: CL_TARGET_OPENCL_VERSION is not defined. Defaulting to 220 (OpenCL 2.2)
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(88,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(97,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(103,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(119,5): warning C5208: typedef 名称中使用的未命名的类不能声明非静态数据成员、成员枚举或成员类以外的成员
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(203,17): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(785,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1438,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2749,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3365,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3977,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(666,31): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(747,50): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(748,70): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(773,42): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(774,49): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(827,47): warning C4244: “=”: 从“const int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(840,51): warning C4244: “=”: 从“const int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(844,51): warning C4244: “=”: 从“const int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(855,47): warning C4244: “=”: 从“const int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(868,37): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(872,35): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(741,37): warning C4101: “theory_coord”: 未引用的局部变量
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(767,29): warning C4101: “theory_coord”: 未引用的局部变量
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(911,46): warning C4244: “参数”: 从“const double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(961,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(962,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(963,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(964,46): warning C4244: “参数”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(991,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(991,38): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1048,27): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1052,31): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1053,35): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1088,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1103,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1110,54): warning C4477: “fprintf”: 格式字符串“%f”需要类型“double”的参数，但可变参数 4 拥有了类型“int”
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1275,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1294,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1334,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1387,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1512,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1515,35): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1516,39): warning C4244: “初始化”: 从“int”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1531,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1541,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1591,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1610,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1718,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1742,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1990,74): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1992,74): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1994,72): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1996,72): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2075,30): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2076,30): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2080,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2088,41): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2089,42): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2097,42): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2109,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2117,40): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2118,43): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2126,42): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2137,96): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2137,78): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2306,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2306,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2307,79): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2307,61): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2309,28): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2312,40): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2316,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2324,32): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2325,35): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2331,22): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2332,22): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2336,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2344,33): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2345,34): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2353,34): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2365,25): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2373,32): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2374,35): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2382,34): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2397,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2397,44): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2430,86): warning C4244: “参数”: 从“int”转换到“const float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2708,72): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2708,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(2991,1): warning C4305: “参数”: 从“double”到“float”截断
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3051,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3051,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3054,33): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3060,33): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3110,50): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3110,39): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3130,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3130,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3503,1): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3508,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3508,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3529,74): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3529,63): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3606,80): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3606,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3614,45): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3614,34): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3647,41): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3708,83): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3708,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3713,44): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3716,48): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3726,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3734,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3735,39): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3741,26): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3742,26): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3746,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3754,37): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3755,38): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3763,38): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3775,29): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3783,36): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3784,39): warning C4244: “初始化”: 从“const _Ty”转换到“int”，可能丢失数据
          with
          [
              _Ty=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3792,38): warning C4244: “初始化”: 从“T”转换到“int”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3807,66): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3807,48): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3981,79): warning C4244: “参数”: 从“int”转换到“const float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4200,58): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4200,49): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4680,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5437,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6091,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6587,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7172,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7757,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4771,86): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4771,71): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4771,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4771,41): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4777,86): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4777,71): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4777,56): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4777,41): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4783,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4783,55): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4783,45): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4783,35): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4789,65): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4789,55): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4789,45): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(4789,35): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5618,44): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5619,44): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5803,106): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5803,77): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5933,109): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(5933,81): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6117,105): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6117,83): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6117,69): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6118,82): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6118,68): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6122,76): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6122,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6126,76): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6126,62): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6275,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6276,51): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6284,84): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6284,66): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6300,55): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6301,55): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6314,84): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6314,66): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6329,69): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6330,69): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6338,69): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6339,69): warning C4244: “参数”: 从“int”转换到“T”，可能丢失数据
          with
          [
              T=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6402,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6403,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6416,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6417,31): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6420,60): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6420,57): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6426,107): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6426,91): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6427,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6427,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6427,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6427,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6439,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6439,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6439,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6439,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6447,119): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6447,98): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6470,41): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6501,41): warning C4244: “初始化”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6504,140): warning C4244: “=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6530,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6531,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6572,127): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6572,111): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6572,84): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6572,68): warning C4244: “参数”: 从“_Tp”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6628,69): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(6629,65): warning C4244: “参数”: 从“int”转换到“_Tp”，可能丢失数据
          with
          [
              _Tp=float
          ]
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7073,1): warning C4244: “*=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7074,1): warning C4244: “*=”: 从“float”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7218,38): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7245,32): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7360,42): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7391,56): warning C4244: “参数”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7652,36): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7671,42): warning C4244: “初始化”: 从“int64”转换到“double”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7693,44): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7695,44): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(7794,1): warning C4267: “初始化”: 从“size_t”转换到“int”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(140,21): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(139): message : 在编译 类 模板 成员函数“float czcv_camera::_Bbox<float>::distance_with(czcv_camera::_Bbox<float> &)”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(3300): message : 查看对正在编译的函数 模板 实例化“float czcv_camera::_Bbox<float>::distance_with(czcv_camera::_Bbox<float> &)”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\detector/base_detector.h(106): message : 查看对正在编译的 类 模板 实例化“czcv_camera::_Bbox<float>”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(141,21): warning C4244: “初始化”: 从“double”转换到“float”，可能丢失数据
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,84): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58): message : 在编译 类 模板 成员函数“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\czcv_center_stage.cpp(1718): message : 查看对正在编译的函数 模板 实例化“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,74): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,65): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,58): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
  person_viewer.cpp
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\person_viewer.cpp(4,1): warning C4005: “BUILDING_DLL”: 宏重定义
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\person_viewer.cpp : message : 参见“BUILDING_DLL”的前一个定义
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\Program\Project\project\czcv_camera_new\lib\include\base/common.h(56,11): warning C4251: “czcv_camera::ImageBlob::frameCopiedBGR”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::ImageBlob”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/status.h(46,21): warning C4251: “czcv_camera::Status::message_”: class“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”需要有 dll 接口由 class“czcv_camera::Status”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\xstring(4905): message : 参见“std::basic_string<char,std::char_traits<char>,std::allocator<char>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(107,20): warning C4251: “czcv_camera::PoolAllocator::budgets_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(108,20): warning C4251: “czcv_camera::PoolAllocator::payouts_lock”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110,46): warning C4251: “czcv_camera::PoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(111,46): warning C4251: “czcv_camera::PoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::PoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(132,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::budgets”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(133,46): warning C4251: “czcv_camera::UnlockedPoolAllocator::payouts”: class“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”需要有 dll 接口由 class“czcv_camera::UnlockedPoolAllocator”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\base/mem_allocator.h(110): message : 参见“std::list<std::pair<size_t,void *>,std::allocator<std::pair<size_t,void *>>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(57,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapDtoS”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage\cam_dewarper.h(58,17): warning C4251: “czcv_camera::CPU_CamDewarper_150::_mapStoD”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::CPU_CamDewarper_150”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(67,5): warning C4275: 非 dll 接口 class“czcv_camera::Abstarct_PersonViewer_DataCallback”用作 dll 接口 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的基
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(24): message : 参见“czcv_camera::Abstarct_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(66): message : 参见“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(140,17): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_frame”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(146,33): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_cv”: class“std::condition_variable”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(583): message : 参见“std::condition_variable”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(147,20): warning C4251: “czcv_camera::Blocked_BGR_PersonViewer_DataCallback::_mtx”: class“std::mutex”需要有 dll 接口由 class“czcv_camera::Blocked_BGR_PersonViewer_DataCallback”的客户端使用
d:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30037\include\mutex(87): message : 参见“std::mutex”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(212,42): warning C4251: “czcv_camera::Base_PersonViewer::_camDewarperPtr”: class“std::shared_ptr<czcv_camera::BaseCamDewarper>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(165): message : 参见“std::shared_ptr<czcv_camera::BaseCamDewarper>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(213,62): warning C4251: “czcv_camera::Base_PersonViewer::_dataCallbackPtr”: class“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(171): message : 参见“std::shared_ptr<czcv_camera::Abstarct_PersonViewer_DataCallback>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(214,42): warning C4251: “czcv_camera::Base_PersonViewer::_rgaInterfacePtr”: class“std::shared_ptr<czcv_camera::rga_interface_t>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(173): message : 参见“std::shared_ptr<czcv_camera::rga_interface_t>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(217,37): warning C4251: “czcv_camera::Base_PersonViewer::_infos”: class“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(217): message : 参见“std::vector<czcv_camera::stInstanceInfo,std::allocator<czcv_camera::stInstanceInfo>>”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(226,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapx”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\include\center_stage/person_viewer.h(227,17): warning C4251: “czcv_camera::Base_PersonViewer::_mapy”: class“cv::Mat”需要有 dll 接口由 class“czcv_camera::Base_PersonViewer”的客户端使用
D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include\opencv2/core/mat.hpp(797): message : 参见“cv::Mat”的声明
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\person_viewer.cpp(194,42): warning C4267: “参数”: 从“size_t”转换到“_Ty”，可能丢失数据
          with
          [
              _Ty=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,84): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58): message : 在编译 类 模板 成员函数“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”时
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\person_viewer.cpp(63): message : 查看对正在编译的函数 模板 实例化“cv::Rect czcv_camera::_Bbox<float>::cv_rect(void) const”的引用
D:\Program\Project\project\czcv_camera_new\lib\src\center_stage\person_viewer.cpp(29): message : 查看对正在编译的 类 模板 实例化“czcv_camera::_Bbox<float>”的引用
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,74): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,65): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
D:\Program\Project\project\czcv_camera_new\lib\include\base/bbox.h(58,58): warning C4244: “参数”: 从“T”转换到“_Tp”，可能丢失数据
          with
          [
              T=float
          ]
          and
          [
              _Tp=int
          ]
  正在生成代码...
    正在创建库 D:/Program/Project/project/czcv_camera_new/build_windows/lib/Release/czcv_camera.lib 和对象 D:/Program/Project/project/czcv_camera_new/build_windows/lib/Release/czcv_camera.exp
  czcv_camera.vcxproj -> D:\Program\Project\project\czcv_camera_new\output\x86\bin\Release\czcv_camera.dll
  'pwsh.exe' 不是内部或外部命令，也不是可运行的程序
  或批处理文件。
