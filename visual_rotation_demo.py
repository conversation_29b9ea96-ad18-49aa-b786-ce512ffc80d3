"""
用ASCII图形直观展示Y轴旋转
"""

def show_rotation_demo():
    print("Y轴旋转的直观演示")
    print("=" * 50)
    
    print("想象您站在房间中央，俯视整个场景:")
    print()
    
    print("初始状态 (yaw = 0°):")
    print("┌─────────────────────────┐")
    print("│         墙面            │")
    print("│         (Z=0)           │")
    print("│                         │")
    print("│                         │")
    print("│           ↑             │")
    print("│           │             │")
    print("│           │ 投影仪朝向   │")
    print("│           │             │")
    print("│           ●             │")
    print("│        投影仪            │")
    print("│                         │")
    print("└─────────────────────────┘")
    print("  ←X轴                Z轴↑")
    print()
    
    print("yaw = +2° 后:")
    print("┌─────────────────────────┐")
    print("│         墙面            │")
    print("│         (Z=0)           │")
    print("│                         │")
    print("│                         │")
    print("│          ↗              │")
    print("│         ╱ 投影仪朝向     │")
    print("│        ╱                │")
    print("│       ╱                 │")
    print("│      ●                  │")
    print("│   投影仪                 │")
    print("│                         │")
    print("└─────────────────────────┘")
    print("  ←X轴                Z轴↑")
    print()
    
    print("旋转动画 (俯视角度):")
    print("步骤1: ● → (朝向正前方)")
    print("       │")
    print()
    print("步骤2: ● ↗ (开始向右偏转)")
    print("       ╱")
    print()
    print("步骤3: ●── (完全向右)")
    print()
    print("这个从 │ 到 ╱ 到 ── 的过程")
    print("在俯视图中确实是逆时针旋转！")
    print()
    
    print("关键理解:")
    print("-" * 30)
    print("• 投影仪'转头'向右看 = 朝向从+Z偏向+X")
    print("• 在俯视图中，从+Z轴向+X轴的旋转是逆时针的")
    print("• 这符合右手坐标系的数学定义")
    print()
    
    print("日常vs数学的区别:")
    print("-" * 30)
    print("• 日常: '向右转' = 顺时针 (基于人的视角)")
    print("• 数学: '绕Y轴正向旋转' = 逆时针 (俯视角度)")
    print("• 两者描述的是同一个物理动作，只是参考系不同")

if __name__ == "__main__":
    show_rotation_demo()
