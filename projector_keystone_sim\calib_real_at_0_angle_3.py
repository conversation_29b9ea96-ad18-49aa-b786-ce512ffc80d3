import json
import math
from dataclasses import dataclass

import cv2
import numpy as np

from scipy.optimize import least_squares


def get_corners_from_left_top(width, height, square_w, square_h, squares_x, squares_y):
    """
    按 从上到下、每行从左到右 的顺序生成角点坐标。
    这里角点是所有网格交点，因此有 (squares_x+1) * (squares_y+1) 个角点。
    """
    corners = []
    for j in range(squares_y + 1):         # 从上到下
        for i in range(squares_x + 1):     # 每行从左到右
            x = i * square_w
            y = j * square_h
            # 防止落在 width / height 边界之外（最后一个点可能等于 width / height）
            x = min(x, width - 1)
            y = min(y, height - 1)
            corners.append((x, y))
    return corners


def get_proj_pts():
    width, height = 1920, 1080
    # squares_x, squares_y = 12, 8
    # square_w = width // squares_x
    # square_h = height // squares_y

    # # 生成角点（从左到右，从上到下）
    # corners = get_corners_from_left_top(
    #     width, height,
    #     square_w, square_h,
    #     squares_x, squares_y
    # )
    # corners = np.array(corners).reshape(squares_y + 1, squares_x + 1, 2)
    # # 去掉外圈，只保留棋盘内部角点
    # corners = corners[1:-1, 1:-1, :].reshape(-1, 2)
    NUM_CALIB_POINTS = 25
    corners = []
    for i in range(NUM_CALIB_POINTS):
        ip_x = ((i % 5) *2 + 11) * 54 + 95
        ip_y = ((i // 5) * 2 + 5) * 54 + 52
        ip = (ip_x, ip_y)
        corners.append(ip)
    corners = np.array(corners, dtype=np.float32)

    return corners


# ---------- 旋转工具 ----------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)


def get_corner(img, corner_init):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    corner_init = np.array(corner_init, dtype=np.float32)
    corners_subpix = cv2.cornerSubPix(
            gray, corner_init, (7, 7), (-1, -1),
            criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
        )
    
    return corners_subpix

def get_point(img, img_path):
    corner_dict = {"cam_200cm_0d.jpg": np.array([[508, 245], [562, 247], [616, 244], [671, 243], [728, 243], 
                                                 [505, 297], [561, 294], [618, 294], [673, 294], [728, 294], 
                                                 [503, 348], [558, 347], [616, 347], [671, 347], [731, 347],
                                                   [499, 402], [556, 402], [614, 401], [670, 403], [732, 402], 
                                                   [496, 458], [556, 458], [615, 458], [673, 460], [733, 458]], dtype=np.float32),
                    "cam_100cm_0d.jpg": np.array([[467, 243], [522, 242], [576, 243], [631, 242], [686, 241], 
                                                 [463, 294], [518, 294], [574, 293], [630, 293], [686, 292], 
                                                 [458, 348], [515, 345], [573, 345], [630, 345], [688, 345],
                                                   [456, 401], [513, 401], [572, 401], [630, 401], [688, 400], 
                                                   [451, 458], [511, 457], [570, 457], [630, 457], [690, 457]], dtype=np.float32),
                    "cam_110cm_0d.jpg": np.array([[249, 151], [350, 151], [455, 149], [559, 147], [665, 146], [772, 145], [880, 143], [988, 141], 
                                                [235, 246], [340, 245], [449, 245], [556, 243], [666, 242], [776, 241], [888, 240], [1002, 239], 
                                                [218, 348], [329, 347], [439, 347], [552, 346], [666, 346], [782, 346], [899, 345], [1016, 345], 
                                                [201, 458], [314, 458], [430, 458], [548, 458], [667, 458], [788, 459], [909, 459], [1032, 459], 
                                                [182, 577], [302, 579], [421, 581], [544, 581], [669, 582], [793, 581], [920, 583], [1048, 584]], dtype=np.float32),
                    "cam_120cm_0d.jpg": np.array([[481, 244], [535, 244], [589, 243], [644, 243], [700, 240], 
                                                  [477, 295], [533, 294], [588, 294], [646, 293], [701, 293], 
                                                  [472, 346], [531, 347], [587, 346], [644, 346], [702, 346], 
                                                  [470, 401], [526, 401], [585, 401], [643, 401], [702, 401], 
                                                  [467, 457], [526, 458], [584, 457], [643, 457], [704, 458]], dtype=np.float32),
                    "cam_150cm_0d.jpg": np.array([[495, 244], [548, 244], [603, 243], [658, 242], [713, 241], 
                                                  [490, 295], [547, 295], [602, 295], [658, 291], [715, 294], 
                                                  [487, 347], [543, 346], [601, 346], [658, 347], [716, 346], 
                                                  [485, 402], [542, 401], [601, 401], [659, 402], [718, 400], 
                                                  [481, 460], [539, 457], [601, 457], [658, 458], [720, 458]], dtype=np.float32)}
    corner_init = corner_dict[img_path]
    corners_subpix = get_corner(img, corner_init)
    return corners_subpix


def get_cam_K():
    # 作为初始值，用标定得到的 Kc0
    # return np.array(
    #     [[820.295, 0,   601.496],
    #      [0,      821.146, 403.593],
    #      [0,      0,   1]],
    #     dtype=np.float64
    # )
    return  np.array(
        [[838.295, 0,        618.496],
         [0,       851.146,  403.593],
         [0,       0,        1],
        ])


# ---------- 投影仪内参 ----------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)


def get_projector_K():
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = 906, Hp
    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    return Kp


# ---------- 从投影仪像素 -> 墙面 3D ----------
def projector_pixel_to_world(u, v, Kp, D):
    """
    u, v: (N,) 投影仪像素坐标
    D: 投影中心到墙面的距离 (m)
    返回: (N,3) 世界坐标点 (X,Y,0)
    """
    uv1 = np.vstack([u, v, np.ones_like(u, dtype=np.float64)])  # (3,N)
    Kinv = np.linalg.inv(Kp)
    d = Kinv @ uv1      # (3,N)
    dx, dy, dz = d

    # 投影仪光心
    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    # 与 Z=0 平面交点
    t = D / dz
    P = Cp + d * t      # (3,N)
    return P.T          # (N,3)


# ---------- 从世界 3D -> 相机像素 ----------
def world_to_camera_pixel(P_w, Kc, dx, D, pitch, yaw, roll):
    """
    P_w: (N,3) 世界坐标点
    dx: 相机中心相对投影仪在 X 方向上的基线 (m)
    D:  与墙面的距离 (决定相机的 Z 坐标)
    返回: (N,2) 相机像素坐标
    """
    # 相机中心（与投影仪同 z = -D，x 上有基线 dx）
    C_cam = np.array([dx, 0.0, -D], dtype=np.float64).reshape(3,1)

    # camera-to-world
    R_c2w = build_rotation(pitch, yaw, roll)
    # world-to-camera
    R_w2c = R_c2w.T

    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy
    return np.vstack([u, v]).T   # (N,2)


# ---------- 固定参数：投影仪初始内参 + 基线 + 相机初始内参 ----------
@dataclass
class SystemFixed:
    Kp0: np.ndarray     # 投影仪初始内参（用来加增量）
    Kc0: np.ndarray     # 相机初始内参（用来加增量）
    dx: float           # 相机相对投影仪在 X 方向上的基线 (米)


# ---------- 残差函数：优化相机内参和投影仪cxp，姿态和D固定 ----------
def residual_params(theta, fixed: SystemFixed, proj_pts, cam_obs, pitch_fixed, yaw_fixed, roll_fixed, D_fixed):
    """
    theta: [d_fx, d_fy, d_cx, d_cy, d_cxp]
    fixed: 包含 Kp0, Kc0, dx
    proj_pts: (N,2)
    cam_obs:  (N,2)
    pitch_fixed, yaw_fixed, roll_fixed, D_fixed: 固定的姿态和距离参数
    返回 (2N,) 残差
    """
    d_fx, d_fy, d_cx, d_cy, d_cxp = theta
    pitch, yaw, roll, D = pitch_fixed, yaw_fixed, roll_fixed, D_fixed
    Kp0, Kc0, dx = fixed.Kp0, fixed.Kc0, fixed.dx

    # 从增量恢复当前投影仪内参
    fxp0, fyp0 = Kp0[0,0], Kp0[1,1]
    cxp0, cyp0 = Kp0[0,2], Kp0[1,2]

    cxp = cxp0 + d_cxp  # 只优化cxp，其他投影仪参数保持固定

    Kp = np.array([[fxp0, 0.0, cxp],
                   [0.0, fyp0, cyp0],
                   [0.0, 0.0, 1.0]], dtype=np.float64)

    # 从增量恢复当前相机内参
    fx0, fy0 = Kc0[0,0], Kc0[1,1]
    cx0, cy0 = Kc0[0,2], Kc0[1,2]

    fx = fx0 + d_fx
    fy = fy0 + d_fy
    cx = cx0 + d_cx
    cy = cy0 + d_cy

    Kc = np.array([[fx, 0.0, cx],
                   [0.0, fy, cy],
                   [0.0, 0.0, 1.0]], dtype=np.float64)

    u_p = proj_pts[:,0]
    v_p = proj_pts[:,1]

    # 投影仪像素 -> 墙面 3D
    Pw = projector_pixel_to_world(u_p, v_p, Kp, D)  # (N,3)

    # 墙面 3D -> 相机像素
    cam_pred = world_to_camera_pixel(Pw, Kc, dx, D, pitch, yaw, roll)  # (N,2)

    res = (cam_pred - cam_obs).ravel()
    return res


if __name__ == '__main__':

    proj_pts = get_proj_pts()
    #proj_pts = np.repeat(proj_pts, 2, axis=0)
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)

    Kp = get_projector_K()
    Kc0 = get_cam_K()
    dx = 0.1


    # 读取真实相机观测
    image = cv2.imread("D:\Dataset\keystone\cam_200cm_0d.jpg")
    cam_obs = np.asarray(get_point(image, "cam_200cm_0d.jpg")) 
    #image = cv2.imread("D:\Dataset\keystone\cam_100cm_0d.jpg")
    #cam_obs = np.asarray(get_point(image, "cam_100cm_0d.jpg")) # (N,2)

    # ---------- 固定参数 ----------
    # 固定姿态和距离参数
    pitch_fixed = 17.0  # 固定pitch角度
    yaw_fixed = -2.0    # 固定yaw角度
    roll_fixed = 0.0    # 固定roll角度
    D_fixed = 2.0       # 固定距离

    print("Fixed parameters:")
    print(f"  pitch: {pitch_fixed} deg")
    print(f"  yaw: {yaw_fixed} deg")
    print(f"  roll: {roll_fixed} deg")
    print(f"  D: {D_fixed} m")

    # ---------- 初始猜测 ----------
    # 优化相机内参增量和投影仪cxp增量，从 0 开始
    theta0 = np.array([0.0, 0.0,       # d_fx, d_fy
                       0.0, 0.0,       # d_cx, d_cy
                       0.0],           # d_cxp
                      dtype=np.float64)
    print("Initial guess [d_fx,d_fy,d_cx,d_cy,d_cxp]:", theta0)

    # ---------- 约束 ----------
    # 对相机内参增量和投影仪cxp增量设置约束
    fx0, fy0 = Kc0[0,0], Kc0[1,1]
    cx0, cy0 = Kc0[0,2], Kc0[1,2]

    # 投影仪初始参数
    fxp0, fyp0 = Kp[0,0], Kp[1,1]
    cxp0, cyp0 = Kp[0,2], Kp[1,2]

    # 相机焦距允许 ±20%
    dfx_min, dfx_max = -0.2 * fx0, 0.2 * fx0
    dfy_min, dfy_max = -0.2 * fy0, 0.2 * fy0

    # 相机主点允许偏移 ±200 像素
    dcx_min, dcx_max = -50.0, 50.0
    dcy_min, dcy_max = -50.0, 50.0

    # 投影仪cxp允许偏移 ±300 像素
    dcxp_min, dcxp_max = -3, 3

    lower = np.array([
        dfx_min, dfy_min,
        dcx_min, dcy_min,
        dcxp_min
    ], dtype=np.float64)

    upper = np.array([
        dfx_max, dfy_max,
        dcx_max, dcy_max,
        dcxp_max
    ], dtype=np.float64)

    fixed = SystemFixed(Kp0=Kp, Kc0=Kc0, dx=dx)

    # ---------- 优化 ----------
    result = least_squares(
        residual_params,
        theta0,
        args=(fixed, proj_pts, cam_obs, pitch_fixed, yaw_fixed, roll_fixed, D_fixed),
        method="trf",
        bounds=(lower, upper),
        verbose=2
    )

    d_fx_est, d_fy_est, d_cx_est, d_cy_est, d_cxp_est = result.x
    print("\nFixed angles (deg):", pitch_fixed, yaw_fixed, roll_fixed)
    print("Fixed D (m):", D_fixed)

    # 估计的相机内参
    fx_est = fx0 + d_fx_est
    fy_est = fy0 + d_fy_est
    cx_est = cx0 + d_cx_est
    cy_est = cy0 + d_cy_est

    Kc_est = np.array([[fx_est, 0.0, cx_est],
                       [0.0, fy_est, cy_est],
                       [0.0, 0.0, 1.0]], dtype=np.float64)

    # 估计的投影仪内参
    cxp_est = cxp0 + d_cxp_est

    Kp_est = np.array([[fxp0, 0.0, cxp_est],
                       [0.0, fyp0, cyp0],
                       [0.0, 0.0, 1.0]], dtype=np.float64)

    print("Estimated camera intrinsics Kc:")
    print(Kc_est)
    print("Estimated projector intrinsics Kp:")
    print(Kp_est)
    print(f"Projector cxp changed from {cxp0:.2f} to {cxp_est:.2f} (delta: {d_cxp_est:.2f})")

    # ---------- 用估计参数重新投影并计算误差 ----------
    Pw_est = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1], Kp_est, D_fixed)
    cam_pred_est = world_to_camera_pixel(Pw_est, Kc_est, dx, D_fixed,
                                         pitch_fixed, yaw_fixed, roll_fixed)

    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)
    print("Mean reprojection error [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

    # ---------- 可视化：红色真值，绿色重投影 ----------
    for i, (x, y) in enumerate(cam_obs):
        cv2.circle(image, (int(x), int(y)), 5, (0, 0, 255), -1)   # obs: red
        x1, y1 = cam_pred_est[i]
        cv2.circle(image, (int(x1), int(y1)), 5, (0, 255, 0), -1) # pred: green

    cv2.imshow("Chessboard with Corners (red=obs, green=pred)", image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    #quit()

    Kc1 = np.array([[859.79920789,  0. , 628.59410574],
                    [0. , 856.97630659, 383.38686511],
                    [0. ,  0. ,          1.        ]], dtype=np.float32)
    Kp1 = np.array([[1.536e+03, 0.000e+00, 9.090e+02],
                    [0.000e+00, 1.536e+03, 1.080e+03],
                    [0.000e+00, 0.000e+00, 1.000e+00]], dtype=np.float32)
    
    Kc2 = np.array([[862.49804941, 0., 627.88973989],
                    [0., 861.24975194, 381.0985397 ],
                    [0.,  0. ,     1.        ]], dtype=np.float32)
    Kp2 = np.array([[1.536e+03, 0.000e+00, 9.090e+02],
                    [0.000e+00, 1.536e+03, 1.080e+03],
                    [0.000e+00, 0.000e+00, 1.000e+00]], dtype=np.float32)
    
    Kp_est = (Kp1 + Kp2) / 2
    Kc_est = (Kc1 + Kc2) / 2

    # ---------- 计算在新的D距离下投影仪角点在相机中的成像点 ----------
    def compute_camera_projection_at_new_distance(proj_pts, Kp_est, Kc_est, dx,
                                                  pitch_fixed, yaw_fixed, roll_fixed,
                                                  D_new):
        """
        使用拟合出来的投影仪与相机内参，计算在新的D距离下投影仪角点在相机中的成像点

        Args:
            proj_pts: (N,2) 投影仪角点像素坐标
            Kp_est: (3,3) 估计的投影仪内参矩阵
            Kc_est: (3,3) 估计的相机内参矩阵
            dx: float 相机相对投影仪在X方向的基线
            pitch_fixed, yaw_fixed, roll_fixed: 固定的相机姿态角度
            D_new: float 新的距离

        Returns:
            cam_pts_new: (N,2) 相机中的成像点坐标
        """
        # 投影仪像素 -> 新距离下的墙面3D点
        Pw_new = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1], Kp_est, D_new)

        # 墙面3D点 -> 相机像素
        cam_pts_new = world_to_camera_pixel(Pw_new, Kc_est, dx, D_new,
                                           pitch_fixed, yaw_fixed, roll_fixed)

        return cam_pts_new

    # 示例：计算在不同距离下的成像点
    distances_to_test = [1.5]  # 测试不同的距离

    print("\n" + "="*60)
    print("计算在不同距离下投影仪角点在相机中的成像点:")
    print("="*60)

    for D_new in distances_to_test:
        cam_pts_new = compute_camera_projection_at_new_distance(
            proj_pts, Kp_est, Kc_est, dx,
            pitch_fixed, yaw_fixed, roll_fixed, D_new
        )

        image = cv2.imread(f"D:\Dataset\keystone\cam_150cm_0d.jpg")
        cam_obs = np.asarray(get_point(image, "cam_150cm_0d.jpg"))
        reproj_err_new = np.linalg.norm(cam_pts_new - cam_obs, axis=1)
        print(f"  重投影误差 [pixels]: mean={reproj_err_new.mean():.2f}, max={reproj_err_new.max():.2f}")

        print(f"\n距离 D = {D_new:.1f}m 时:")
        print(f"  前5个角点的相机成像坐标:")
        for i in range(len(cam_pts_new)):
            u, v = cam_pts_new[i]
            print(f"    点{i+1}: ({u:.2f}, {v:.2f}), ({cam_obs[i, 0]:.2f}, {cam_obs[i, 1]:.2f})")

        # 计算成像点的范围
        u_min, u_max = cam_pts_new[:, 0].min(), cam_pts_new[:, 0].max()
        v_min, v_max = cam_pts_new[:, 1].min(), cam_pts_new[:, 1].max()
        print(f"  成像范围: u=[{u_min:.1f}, {u_max:.1f}], v=[{v_min:.1f}, {v_max:.1f}]")

        # 检查是否在相机视野内（假设相机分辨率为1280x720）
        cam_width, cam_height = 1280, 720
        in_view = ((cam_pts_new[:, 0] >= 0) & (cam_pts_new[:, 0] < cam_width) &
                   (cam_pts_new[:, 1] >= 0) & (cam_pts_new[:, 1] < cam_height))
        in_view_count = in_view.sum()
        print(f"  在相机视野内的点数: {in_view_count}/{len(cam_pts_new)} ({100*in_view_count/len(cam_pts_new):.1f}%)")

    # 返回函数供外部使用
    print(f"\n可以使用 compute_camera_projection_at_new_distance() 函数计算任意距离下的成像点")

    # 将函数添加到全局命名空间，方便后续使用
    globals()['compute_camera_projection_at_new_distance'] = compute_camera_projection_at_new_distance