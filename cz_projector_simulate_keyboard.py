import cv2
import numpy as np
import math
import os

# -------- rotations --------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # R = Rz * Ry * Rx
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

# -------- intrinsics from throw ratio --------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    # throw_ratio = distance / image_width  ->  fovx = 2*atan( image_width/(2*distance) ) = 2*atan(1/(2*throw_ratio))
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# -------- camera intrinsics from horizontal FOV --------
def intrinsics_from_hfov(w, h, hfov_deg, cx, cy):
    fovx = math.radians(hfov_deg)
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)
    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)

# -------- homographies --------
def homography_from_rotation(K, R):
    H = K @ R @ np.linalg.inv(K)
    return H / H[2,2]

def warp_image(img, H, dsize):
    return cv2.warpPerspective(img, H, dsize, flags=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)

def render_simulation(img, K, pitch, yaw, roll, W, H):
    R = build_rotation(pitch, yaw, roll)
    H_forward = homography_from_rotation(K, R)

    # 让画布更大一些，便于观察
    T = np.array([[1.0, 0.0, W/2.0],
                  [0.0, 1.0, H/2.0],
                  [0.0, 0.0, 1.0]], dtype=np.float64)
    H_shift = T @ H_forward
    warped = warp_image(img, H_shift, (2*W, 2*H))
    return warped

# -------- compute wall-plane (Z=0) intersections for 4 image corners --------
def corners_world_xy(K, pitch, yaw, roll, W, H, D_m):
    """
    Projector center C = (0,0,-D), wall plane Z=0.
    For pixel p = [u,v,1]^T, ray dir_world = R @ K^{-1} p (not normalized).
    Intersection: t = D / dir_z; (X,Y) = t * (dir_x, dir_y).
    """
    R = build_rotation(pitch, yaw, roll)
    Kinv = np.linalg.inv(K)

    # four corners in pixel coords (u,v)
    corners = np.array([[0, 0, 1],
                        [W-1, 0, 1],
                        [W-1, H-1, 1],
                        [0, H-1, 1]], dtype=np.float64).T  # 3x4

    dirs = R @ (Kinv @ corners)  # 3x4
    xs, ys, zs = dirs[0, :], dirs[1, :], dirs[2, :]

    XY = []
    for i in range(4):
        dz = zs[i]
        if dz <= 1e-9:
            # 不与Z=0相交（或平行/背对），做保护
            XY.append((float('nan'), float('nan')))
        else:
            t = D_m / dz
            XY.append((t * xs[i], t * ys[i]))
    return XY  # list of (X,Y) in meters

# -------- compute camera view of the projected corners --------
def camera_view_of_corners(K_proj, pitch_proj, yaw_proj, roll_proj, W, H, D_m, dx_cam, pitch_cam, yaw_cam, roll_cam):
    """
    计算从camera角度看到的投影仪四个角点的位置
    K_proj: 投影仪内参
    pitch_proj, yaw_proj, roll_proj: 投影仪姿态
    dx_cam: camera相对投影仪在X方向的距离
    pitch_cam, yaw_cam, roll_cam: camera相对投影仪的姿态
    """
    # 1. 计算投影仪四个角点在墙面上的世界坐标
    xy_list = corners_world_xy(K_proj, pitch_proj, yaw_proj, roll_proj, W, H, D_m)

    # 2. camera中心位置（相对于投影仪）
    C_cam = np.array([dx_cam, 0.0, -D_m], dtype=np.float64).reshape(3,1)

    # 3. camera的旋转矩阵
    R_cam_c2w = build_rotation(pitch_cam, yaw_cam, roll_cam)
    R_w2c = R_cam_c2w.T

    # 4. 将墙面点转换到camera坐标系
    camera_corners = []
    for x, y in xy_list:
        if not (math.isnan(x) or math.isnan(y)):
            P_w = np.array([x, y, 0.0], dtype=np.float64).reshape(3,1)
            P_c = R_w2c @ (P_w - C_cam)
            camera_corners.append((P_c[0,0], P_c[1,0], P_c[2,0]))
        else:
            camera_corners.append((float('nan'), float('nan'), float('nan')))

    return camera_corners

# -------- projector pixel to camera pixel conversion --------
def projector_pixel_to_world(u, v, Kp, D, pitch_proj=0.0, yaw_proj=0.0, roll_proj=0.0):
    """
    投影仪像素坐标转换为墙面世界坐标
    u, v: 投影仪像素坐标 (可以是数组)
    Kp: 投影仪内参矩阵
    D: 投影仪到墙面距离
    pitch_proj, yaw_proj, roll_proj: 投影仪的旋转角度
    返回: 墙面上的世界坐标点 (N,3)
    """
    u = np.asarray(u, dtype=np.float64)
    v = np.asarray(v, dtype=np.float64)

    # 构造齐次像素坐标
    uv1 = np.vstack([u.ravel(), v.ravel(), np.ones_like(u.ravel())])  # (3,N)

    # 投影仪内参逆矩阵
    Kinv = np.linalg.inv(Kp)
    d_local = Kinv @ uv1      # (3,N) 投影仪坐标系中的射线方向

    # 投影仪的旋转矩阵
    R_proj = build_rotation(pitch_proj, yaw_proj, roll_proj)

    # 将射线方向转换到世界坐标系
    d_world = R_proj @ d_local  # (3,N)

    # 投影仪中心在世界坐标系中的位置
    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    # 射线与Z=0平面的交点
    # P = Cp + t * d_world, 其中P[2] = 0
    # 0 = Cp[2] + t * d_world[2]
    # t = -Cp[2] / d_world[2] = D / d_world[2]
    dz_world = d_world[2, :]  # 世界坐标系中的Z方向分量

    # 避免除零
    valid_mask = np.abs(dz_world) > 1e-6
    t = np.zeros_like(dz_world)
    t[valid_mask] = D / dz_world[valid_mask]

    # 世界坐标点
    P_world = Cp + d_world * t.reshape(1,-1)  # (3,N)
    return P_world.T          # (N,3)

def world_to_camera_pixel(P_w, Kc, dx_cam, D, pitch_cam, yaw_cam, roll_cam):
    """
    世界坐标转换为camera像素坐标
    P_w: (N,3) 世界坐标点
    Kc: camera内参矩阵
    dx_cam: camera相对投影仪在X方向的距离
    D: 到墙面的距离
    pitch_cam, yaw_cam, roll_cam: camera姿态
    返回: (N,2) camera像素坐标
    """
    # camera中心位置
    C_cam = np.array([dx_cam, 0.0, -D], dtype=np.float64).reshape(3,1)

    # camera旋转矩阵
    R_c2w = build_rotation(pitch_cam, yaw_cam, roll_cam)
    R_w2c = R_c2w.T

    # 转换到camera坐标系
    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy

    return np.vstack([u, v]).T   # (N,2)

def render_camera_view_improved(proj_img, Kp, dx_cam, D_m, pitch_cam, yaw_cam, roll_cam, cam_w, cam_h, pitch_proj=0.0, yaw_proj=0.0, roll_proj=0.0):
    """
    改进的camera视角渲染 - 基于真实几何投影
    考虑投影仪的旋转对camera位置的影响
    """
    # 创建camera视角的图像
    camera_img = np.zeros((cam_h, cam_w, 3), dtype=np.uint8)

    proj_h, proj_w = proj_img.shape[:2]

    # 定义camera内参（简化版本）
    cam_fov_h = 60.0  # camera水平视场角（度）
    cam_fx = (cam_w / 2.0) / math.tan(math.radians(cam_fov_h / 2.0))
    cam_fy = cam_fx  # 假设像素是正方形
    cam_cx, cam_cy = cam_w / 2.0, cam_h / 2.0
    Kc = np.array([[cam_fx, 0, cam_cx], [0, cam_fy, cam_cy], [0, 0, 1]], dtype=np.float64)

    # 投影仪四个角点在投影仪像素坐标系中的位置
    proj_corners_uv = np.array([
        [0, 0],           # 左上
        [proj_w-1, 0],    # 右上
        [proj_w-1, proj_h-1],  # 右下
        [0, proj_h-1]     # 左下
    ], dtype=np.float64)

    # 投影仪角点 -> 墙面世界坐标（考虑投影仪旋转）
    proj_corners_world = []
    for u, v in proj_corners_uv:
        # 投影仪像素到射线方向（投影仪坐标系）
        uv1 = np.array([u, v, 1.0])
        Kinv = np.linalg.inv(Kp)
        d_local = Kinv @ uv1

        # 将射线方向转换到世界坐标系
        d_world = R_proj @ d_local

        # 投影仪中心位置
        Cp = np.array([0.0, 0.0, -D_m])

        # 与Z=0平面的交点
        if abs(d_world[2]) > 1e-6:
            t = D_m / d_world[2]
            P = Cp + d_world * t
            proj_corners_world.append([P[0], P[1], 0.0])
        else:
            # 射线平行于Z=0平面，使用默认点
            proj_corners_world.append([0.0, 0.0, 0.0])

    proj_corners_world = np.array(proj_corners_world, dtype=np.float64)

    # 世界坐标 -> camera像素坐标
    # 投影仪的旋转矩阵和位置
    R_proj = build_rotation(pitch_proj, yaw_proj, roll_proj)
    C_proj = np.array([0.0, 0.0, -D_m])  # 投影仪在世界坐标系中的位置

    # Camera在投影仪坐标系中的相对位置
    cam_relative_pos = np.array([dx_cam, 0.0, 0.0])

    # Camera在世界坐标系中的实际位置（考虑投影仪旋转）
    C_cam = C_proj + R_proj @ cam_relative_pos

    # Camera的姿态矩阵（相对于投影仪的旋转）
    R_cam_relative = build_rotation(pitch_cam, yaw_cam, roll_cam)

    # Camera在世界坐标系中的实际姿态（投影仪旋转 + camera相对旋转）
    R_c2w = R_proj @ R_cam_relative
    R_w2c = R_c2w.T

    # 转换到camera坐标系
    cam_corners_3d = []
    for P_w in proj_corners_world:
        Pc = R_w2c @ (P_w - C_cam)
        cam_corners_3d.append(Pc)

    cam_corners_3d = np.array(cam_corners_3d)

    # 投影到camera像素坐标
    cam_corners_uv = []
    for Pc in cam_corners_3d:
        if Pc[2] > 0.01:  # 确保在camera前方
            u = cam_fx * (Pc[0] / Pc[2]) + cam_cx
            v = cam_fy * (Pc[1] / Pc[2]) + cam_cy
            cam_corners_uv.append([u, v])
        else:
            cam_corners_uv.append([0, 0])  # 无效点

    cam_corners_uv = np.array(cam_corners_uv, dtype=np.float32)

    # 检查是否有有效的投影
    valid_corners = []
    for corner in cam_corners_uv:
        if 0 <= corner[0] < cam_w and 0 <= corner[1] < cam_h:
            valid_corners.append(corner)

    if len(valid_corners) >= 3:
        # 使用透视变换将投影仪图像映射到camera视角
        src_corners = proj_corners_uv.astype(np.float32)
        dst_corners = cam_corners_uv

        # 计算透视变换矩阵
        try:
            M = cv2.getPerspectiveTransform(src_corners, dst_corners)
            # 应用透视变换
            camera_img = cv2.warpPerspective(proj_img, M, (cam_w, cam_h))
        except:
            # 如果透视变换失败，使用简单的缩放和平移
            scale = 0.8
            offset_x = int((cam_w - proj_w * scale) / 2)
            offset_y = int((cam_h - proj_h * scale) / 2)

            resized = cv2.resize(proj_img, (int(proj_w * scale), int(proj_h * scale)))
            if offset_x >= 0 and offset_y >= 0:
                end_x = min(offset_x + resized.shape[1], cam_w)
                end_y = min(offset_y + resized.shape[0], cam_h)
                camera_img[offset_y:end_y, offset_x:end_x] = resized[:end_y-offset_y, :end_x-offset_x]
    else:
        # 如果没有足够的有效角点，显示一个简单的缩放版本
        scale = 0.6
        resized = cv2.resize(proj_img, (int(cam_w * scale), int(cam_h * scale)))
        offset_x = (cam_w - resized.shape[1]) // 2
        offset_y = (cam_h - resized.shape[0]) // 2
        camera_img[offset_y:offset_y+resized.shape[0], offset_x:offset_x+resized.shape[1]] = resized

    # 添加camera视角标识和信息
    cv2.rectangle(camera_img, (2, 2), (cam_w-2, cam_h-2), (0, 255, 0), 2)

    # 添加角点标记（如果可见）
    for i, corner in enumerate(cam_corners_uv):
        x, y = int(corner[0]), int(corner[1])
        if 0 <= x < cam_w and 0 <= y < cam_h:
            cv2.circle(camera_img, (x, y), 5, (0, 255, 255), -1)
            cv2.putText(camera_img, str(i), (x+10, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)

    return camera_img

def render_camera_view_realistic(proj_img, Kp, dx_cam, D_m, pitch_cam, yaw_cam, roll_cam, cam_w, cam_h, pitch_proj=0.0, yaw_proj=0.0, roll_proj=0.0):
    """
    更真实的camera视角渲染 - 基于像素级映射
    考虑投影仪的旋转对camera位置的影响
    """
    camera_img = np.zeros((cam_h, cam_w, 3), dtype=np.uint8)
    proj_h, proj_w = proj_img.shape[:2]

    # Camera内参
    cam_fov_h = 70.0  # camera水平视场角
    cam_fx = (cam_w / 2.0) / math.tan(math.radians(cam_fov_h / 2.0))
    cam_fy = cam_fx
    cam_cx, cam_cy = cam_w / 2.0, cam_h / 2.0

    # 投影仪的旋转矩阵和位置
    R_proj = build_rotation(pitch_proj, yaw_proj, roll_proj)
    C_proj = np.array([0.0, 0.0, -D_m])  # 投影仪在世界坐标系中的位置

    # Camera在投影仪坐标系中的相对位置
    cam_relative_pos = np.array([dx_cam, 0.0, 0.0])

    # Camera在世界坐标系中的实际位置（考虑投影仪旋转）
    C_cam = C_proj + R_proj @ cam_relative_pos

    # Camera的姿态矩阵（相对于投影仪的旋转）
    R_cam_relative = build_rotation(pitch_cam, yaw_cam, roll_cam)

    # Camera在世界坐标系中的实际姿态（投影仪旋转 + camera相对旋转）
    R_c2w = R_proj @ R_cam_relative
    R_w2c = R_c2w.T

    # 为camera图像的每个像素找到对应的投影仪像素
    step = 4  # 增加降采样步长以提高性能
    for v_cam in range(0, cam_h, step):
        for u_cam in range(0, cam_w, step):
            # Camera像素 -> Camera射线
            cam_ray = np.array([
                (u_cam - cam_cx) / cam_fx,
                (v_cam - cam_cy) / cam_fy,
                1.0
            ])

            # 转换到世界坐标系
            world_ray = R_c2w @ cam_ray

            # 与Z=0平面的交点
            if abs(world_ray[2]) > 1e-6:
                t = -C_cam[2] / world_ray[2]  # 修正：使用camera位置计算交点
                if t > 0:  # 射线向前
                    world_point = C_cam + world_ray * t

                    # 世界坐标 -> 投影仪像素
                    # 考虑投影仪的旋转
                    proj_point_world = world_point - C_proj  # 相对于投影仪的位置
                    proj_point_local = R_proj.T @ proj_point_world  # 转换到投影仪坐标系

                    if proj_point_local[2] > 1e-6:  # 在投影仪前方
                        # 投影到投影仪像素
                        proj_3d = proj_point_local / proj_point_local[2]  # 归一化
                        proj_pixel = Kp @ proj_3d

                        u_proj, v_proj = int(proj_pixel[0]), int(proj_pixel[1])

                        # 检查是否在投影仪图像范围内
                        if 0 <= u_proj < proj_w and 0 <= v_proj < proj_h:
                            # 复制像素值到camera图像
                            for dy in range(step):
                                for dx in range(step):
                                    cam_y, cam_x = v_cam + dy, u_cam + dx
                                    if cam_y < cam_h and cam_x < cam_w:
                                        camera_img[cam_y, cam_x] = proj_img[v_proj, u_proj]

    # 添加camera视角标识
    cv2.rectangle(camera_img, (2, 2), (cam_w-2, cam_h-2), (0, 255, 0), 2)
    cv2.putText(camera_img, "Camera View", (10, 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

    return camera_img

def main():
    print("Starting camera-projector simulation...")

    # ------------- parameters -------------
    target_path = r"D:\20251027-113839.png"
    # 投影仪参数
    W, H = 1920, 1080
    throw_ratio = 0.8
    cx, cy = 906, H         # 主点在底边中心
    D_m = 1.6                # 投影距离（米），投影仪光心到墙面的距离
    dx_cam = 0.1              # camera相对投影仪在X方向的距离（米）

    # camera参数
    Wc, Hc = 1280, 720       # camera分辨率
    #hfov_deg = 75.0          # camera水平视场角
    #cxc, cyc = Wc / 2.0, Hc / 2.0  # camera主点

    print(f"Projector resolution: {W}x{H}")
    print(f"Camera resolution: {Wc}x{Hc}")
    print(f"Camera distance: {dx_cam}m")

    # 创建测试图像或加载现有图像
    if os.path.exists(target_path):
        target = cv2.imread(target_path)
        target = cv2.resize(target, (W, H))
    else:
        # 创建一个测试图像
        target = np.zeros((H, W, 3), dtype=np.uint8)
        # 添加一些测试图案
        cv2.rectangle(target, (100, 100), (W-100, H-100), (255, 255, 255), 2)
        cv2.rectangle(target, (W//4, H//4), (3*W//4, 3*H//4), (0, 255, 0), 2)
        cv2.circle(target, (W//2, H//2), 50, (0, 0, 255), -1)
        cv2.putText(target, "TEST IMAGE", (W//2-100, H//2+100), cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 0), 3)

    # 计算内参矩阵
    K = intrinsics_from_throw_ratio(W, H, throw_ratio, cx, cy)  # 投影仪内参
    #Kc = intrinsics_from_hfov(Wc, Hc, hfov_deg, cxc, cyc)      # camera内参

    # 投影仪初始姿态（相对于墙面）
    pitch_proj, yaw_proj, roll_proj = 0.0, 0.0, 0.0

    # 设置camera相对投影仪的姿态：向投影方向yaw 2°，向上pitch 17°
    # 注意：投影仪在camera左侧(-X方向)，所以朝向投影仪方向应该是负的yaw值
    # yaw = -2°表示从朝向墙面(+Z)向左侧(-X)偏转，即朝向投影仪方向
    # 完全朝向投影仪需要yaw = -90°
    pitch_cam, yaw_cam, roll_cam = 17.0, -2.0, 0.0
    step = 0.5

    # Camera视角渲染模式
    camera_render_mode = 1  # 0: 改进版本, 1: 真实版本

    # Camera跟随模式：camera是否随投影仪一起旋转
    camera_follow_projector = True  # True: camera随投影仪旋转, False: camera独立旋转

    print("Creating windows...")
    cv2.namedWindow("Projector View", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Projector View", 1920, 1080)
    cv2.namedWindow("Camera View", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Camera View", 1280, 720)
    print("Windows created. Starting main loop...")

    while True:
        # 渲染投影仪视角
        warped = render_simulation(target, K, pitch_proj, yaw_proj, roll_proj, W, H)

        # 计算实际的camera姿态（考虑是否跟随投影仪）
        if camera_follow_projector:
            # Camera随投影仪一起旋转，在投影仪坐标系基础上加上相对角度
            actual_pitch_cam = pitch_proj + pitch_cam
            actual_yaw_cam = yaw_proj + yaw_cam
            actual_roll_cam = roll_proj + roll_cam
        else:
            # Camera独立旋转
            actual_pitch_cam = pitch_cam
            actual_yaw_cam = yaw_cam
            actual_roll_cam = roll_cam

        # 渲染camera视角（根据模式选择）
        if camera_render_mode == 0:
            camera_view = render_camera_view_improved(target, K, dx_cam, D_m, actual_pitch_cam, actual_yaw_cam, actual_roll_cam, Wc, Hc, pitch_proj, yaw_proj, roll_proj)
        else:
            camera_view = render_camera_view_realistic(target, K, dx_cam, D_m, actual_pitch_cam, actual_yaw_cam, actual_roll_cam, Wc, Hc, pitch_proj, yaw_proj, roll_proj)
        print(camera_view.shape)
        # 计算四角世界坐标（米）
        xy_list = corners_world_xy(K, pitch_proj, yaw_proj, roll_proj, W, H, D_m)
        # 计算从camera角度看到的投影角点（使用实际camera角度）
        cam_corners = camera_view_of_corners(K, pitch_proj, yaw_proj, roll_proj, W, H, D_m, dx_cam, actual_pitch_cam, actual_yaw_cam, actual_roll_cam)
       
        # 文本叠加
        y0 = 40
        dy = 30
        h1 = xy_list[2][1] - xy_list[1][1]
        h0 = xy_list[3][1] - xy_list[0][1]
        h_diff = (h0 - h1) * 100

        w1 = xy_list[1][0] - xy_list[0][0]
        w0 = xy_list[2][0] - xy_list[3][0]
        w_diff = (w0 - w1) * 100

        render_mode_text = "Improved" if camera_render_mode == 0 else "Realistic"
        follow_mode_text = "Following" if camera_follow_projector else "Independent"
        info_lines = [
            f"Projector - Pitch: {pitch_proj:.1f} deg   Yaw: {yaw_proj:.1f} deg   Roll: {roll_proj:.1f} deg   D: {D_m:.3f} m",
            f"Camera Relative - Pitch: {pitch_cam:.1f} deg   Yaw: {yaw_cam:.1f} deg   Roll: {roll_cam:.1f} deg   dx: {dx_cam:.3f} m",
            f"Camera Actual - Pitch: {actual_pitch_cam:.1f} deg   Yaw: {actual_yaw_cam:.1f} deg   Roll: {actual_roll_cam:.1f} deg",
            f"Camera Mode: {follow_mode_text} | Render: {render_mode_text} (Press 'C'/'M' to switch)",
            "Projector corners @ Z=0 plane (meters):",
            f"TL (0,0):      X={xy_list[0][0]:.4f}, Y={xy_list[0][1]:.4f}",
            f"TR (W-1,0):    X={xy_list[1][0]:.4f}, Y={xy_list[1][1]:.4f}",
            f"BR (W-1,H-1):  X={xy_list[2][0]:.4f}, Y={xy_list[2][1]:.4f}",
            f"BL (0,H-1):    X={xy_list[3][0]:.4f}, Y={xy_list[3][1]:.4f}",
            "Camera view of corners (camera coords):",
            f"TL: X={cam_corners[0][0]:.4f}, Y={cam_corners[0][1]:.4f}, Z={cam_corners[0][2]:.4f}",
            f"TR: X={cam_corners[1][0]:.4f}, Y={cam_corners[1][1]:.4f}, Z={cam_corners[1][2]:.4f}",
            f"H1 - H2:  {h_diff:.4f} cm   W1 - W2:  {w_diff:.4f} cm",
            "(W/S: proj pitch, A/D: proj yaw, Z/X: proj roll)",
            "(T/G: cam pitch, F/H: cam yaw [-:toward proj], R/Y: cam roll, V/B: cam distance, Q/E: proj distance)",
            "(C: toggle follow mode, M: switch render mode, ESC: quit)"
        ]

        # 绘制文本（绿色）
        for i, line in enumerate(info_lines):
            y = y0 + i * dy
            cv2.putText(warped, line, (30, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0,255,0), 2, cv2.LINE_AA)

        # 在camera视角图像上添加简单信息
        cv2.putText(camera_view, "Camera View", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0,255,0), 2, cv2.LINE_AA)
        cv2.putText(camera_view, f"Rel: P={pitch_cam:.1f} Y={yaw_cam:.1f} R={roll_cam:.1f}",
                   (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,0), 2, cv2.LINE_AA)
        cv2.putText(camera_view, f"Act: P={actual_pitch_cam:.1f} Y={actual_yaw_cam:.1f} R={actual_roll_cam:.1f}",
                   (10, 85), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0,255,255), 2, cv2.LINE_AA)
        cv2.putText(camera_view, f"Mode: {follow_mode_text}", (10, 110), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255,255,0), 2, cv2.LINE_AA)

        # 显示两个窗口
        cv2.imshow("Projector View", warped)
        cv2.imshow("Camera View", camera_view)

        key = cv2.waitKey(30) & 0xFF
        if key == 27:  # ESC
            break
        # 投影仪控制
        elif key == ord('w'):
            pitch_proj += step
        elif key == ord('s'):
            pitch_proj -= step
        elif key == ord('a'):
            yaw_proj -= step
        elif key == ord('d'):
            yaw_proj += step
        elif key == ord('z'):
            roll_proj -= step
        elif key == ord('x'):
            roll_proj += step
        # camera控制
        elif key == ord('t'):
            pitch_cam += step
        elif key == ord('g'):
            pitch_cam -= step
        elif key == ord('f'):
            yaw_cam -= step
        elif key == ord('h'):
            yaw_cam += step
        elif key == ord('r'):
            roll_cam -= step
        elif key == ord('y'):
            roll_cam += step
        # 距离控制
        elif key == ord('q'):
            D_m = max(0.1, D_m - 0.05)   # 最小 0.1 m，避免为零
        elif key == ord('e'):
            D_m += 0.05
        elif key == ord('v'):
            dx_cam = max(0.01, dx_cam - 0.01)  # camera距离控制
        elif key == ord('b'):
            dx_cam += 0.01
        # 切换camera渲染模式
        elif key == ord('m') or key == ord('M'):
            camera_render_mode = 1 - camera_render_mode  # 在0和1之间切换
            mode_text = "Improved" if camera_render_mode == 0 else "Realistic"
            print(f"Switched to {mode_text} camera rendering mode")
        # 切换camera跟随模式
        elif key == ord('c') or key == ord('C'):
            camera_follow_projector = not camera_follow_projector
            follow_text = "Following projector" if camera_follow_projector else "Independent"
            print(f"Camera mode: {follow_text}")

    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()