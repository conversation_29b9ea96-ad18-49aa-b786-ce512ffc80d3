import cv2
import numpy as np
import math

# =================== 3D场景参数设置 ===================
# 墙面范围（米）：宽2m x 高1.5m
WALL_WIDTH, WALL_HEIGHT = 5.0, 3
# 棋盘格参数（10x10方格，每个方格0.1m）
CHESS_ROWS, CHESS_COLS = 9, 16
SQUARE_SIZE = 0.2
# 相机初始位置（距离墙面3m）
CAMERA_DIST = 1.5
# 图像分辨率
IMG_WIDTH, IMG_HEIGHT = 1280, 720

# =================== 相机内参（模拟真实镜头） ===================
focal_length = 400  # 焦距（像素）
camera_matrix = np.array([
    [focal_length, 0, IMG_WIDTH//2],
    [0, focal_length, IMG_HEIGHT//2],
    [0, 0, 1]
], dtype=np.float32)

# 生成棋盘格3D坐标（墙面z=0）
chessboard_points = []
for i in range(CHESS_ROWS + 1):
    for j in range(CHESS_COLS + 1):
        x = -WALL_WIDTH/2 + j * SQUARE_SIZE
        y = -WALL_HEIGHT/2 + i * SQUARE_SIZE
        chessboard_points.append([x, y, 0])
chessboard_points = np.array(chessboard_points, dtype=np.float32)

# =================== 交互控制函数 ===================
def update_view(*args):
    """实时更新棋盘格位置和相机视角"""
    # 棋盘格平移（映射到墙的范围）
    tx = (cv2.getTrackbarPos('Chess X', 'Camera View') - 100) * 0.01  # -1 ~ 1m
    ty = (cv2.getTrackbarPos('Chess Y', 'Camera View') - 75) * 0.01   # -0.75 ~ 0.75m
    
    # 相机旋转（-90° ~ 90°）
    rx = cv2.getTrackbarPos('Camera Rx', 'Camera View') - 90  # 绕X轴
    ry = cv2.getTrackbarPos('Camera Ry', 'Camera View') - 90  # 绕Y轴
    rz = cv2.getTrackbarPos('Camera Rz', 'Camera View') - 90  # 绕Z轴
    
    # 生成棋盘格世界坐标（应用平移）
    chessboard_world = chessboard_points.copy()
    chessboard_world[:, 0] += tx
    chessboard_world[:, 1] += ty
    
    # 计算相机旋转矩阵
    def euler_to_rot(rx, ry, rz):
        """欧拉角转旋转矩阵（Z-Y-X顺序）"""
        rx, ry, rz = np.radians([rx, ry, rz])
        Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                      [np.sin(rz), np.cos(rz), 0],
                      [0, 0, 1]])
        Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                      [0, 1, 0],
                      [-np.sin(ry), 0, np.cos(ry)]])
        Rx = np.array([[1, 0, 0],
                      [0, np.cos(rx), -np.sin(rx)],
                      [0, np.sin(rx), np.cos(rx)]])
        return Rz @ Ry @ Rx
    
    R = euler_to_rot(rx, ry, rz)
    rvec, _ = cv2.Rodrigues(R)
    
    # 相机位置（固定在(0,0,CAMERA_DIST)）
    tvec = np.array([0, 0, CAMERA_DIST], dtype=np.float32)
    
    # 投影到2D图像
    img_points, _ = cv2.projectPoints(
        chessboard_world, rvec, tvec, camera_matrix, None
    )
    
    # 创建空白图像
    img = np.zeros((IMG_HEIGHT, IMG_WIDTH, 3), dtype=np.uint8)
    
    # 绘制棋盘格（绿色线条）
    img_points = np.int32(img_points)
    
    # 绘制水平线
    for i in range(CHESS_ROWS + 1):
        start_idx = i * (CHESS_COLS + 1)
        end_idx = start_idx + CHESS_COLS + 1
        cv2.polylines(img, [img_points[start_idx:end_idx]], 
                      False, (0, 255, 0), 1)
    
    # 绘制垂直线
    for j in range(CHESS_COLS + 1):
        start_idx = j
        points = [img_points[start_idx + k * (CHESS_COLS + 1)] 
                  for k in range(CHESS_ROWS + 1)]
        cv2.polylines(img, [np.array(points)], False, (0, 255, 0), 1)
    
    # 添加墙面参考线（灰色）
    cv2.rectangle(img, (0, 0), (IMG_WIDTH - 1, IMG_HEIGHT - 1), (100, 100, 100), 1)
    
    # 显示参数信息
    cv2.putText(img, f"Chess: X={tx:.2f}m Y={ty:.2f}m", (10, 20), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(img, f"Cam: Rx={rx}° Ry={ry}° Rz={rz}°", (10, 40), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    cv2.imshow('Camera View', img)

# =================== 初始化界面 ===================
cv2.namedWindow('Camera View')
cv2.createTrackbar('Chess X', 'Camera View', 100, 300, update_view)  # -1~1m
cv2.createTrackbar('Chess Y', 'Camera View', 100, 300, update_view)   # -0.75~0.75m
cv2.createTrackbar('Camera Rx', 'Camera View', 90, 180, update_view)  # -90~90°
cv2.createTrackbar('Camera Ry', 'Camera View', 90, 180, update_view)
cv2.createTrackbar('Camera Rz', 'Camera View', 90, 180, update_view)

# 初始显示
update_view()

# 交互循环
print("🚀 欢迎使用3D摄像头模拟器！")
print("📌 操作提示：")
print("  - 棋盘格移动：用 'Chess X/Y' 调整位置")
print("  - 相机旋转：用 'Camera Rx/Ry/Rz' 旋转视角")
print("  - 按 'q' 退出程序")
print("💡 小技巧：旋转相机时，棋盘格会像真实镜头一样倾斜哦！")
print("👉 现在试试把棋盘格移到墙角，再旋转相机看透视变化~")

cv2.waitKey(0)
cv2.destroyAllWindows()