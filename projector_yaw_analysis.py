"""
分析投影仪yaw旋转方向
"""
import numpy as np
import math

def rot_y(deg):
    """Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def analyze_projector_yaw():
    print("投影仪Yaw旋转方向分析")
    print("=" * 50)
    
    # 坐标系设置
    print("坐标系设置:")
    print("• 投影仪位置: (0, 0, -1.5)")
    print("• 墙面位置: Z = 0")
    print("• 投影仪初始朝向: +Z方向（朝向墙面）")
    print("• X轴: 水平方向，+X向右")
    print("• Y轴: 垂直方向，+Y向上")
    print("• Z轴: 深度方向，+Z向前（朝向墙面）")
    print()
    
    # 投影仪初始朝向
    proj_initial_direction = np.array([0, 0, 1])  # 朝向+Z（墙面）
    print(f"投影仪初始朝向: {proj_initial_direction} (朝向墙面)")
    print()
    
    # 分析不同yaw角度
    print("投影仪Yaw旋转分析:")
    print("-" * 40)
    
    yaw_angles = [-5.0, -2.0, 0.0, 2.0, 5.0]
    
    for yaw in yaw_angles:
        R_y = rot_y(yaw)
        new_direction = R_y @ proj_initial_direction
        
        print(f"投影仪 yaw = {yaw:+4.1f}°:")
        print(f"  新朝向: ({new_direction[0]:+6.3f}, {new_direction[1]:+6.3f}, {new_direction[2]:+6.3f})")
        
        # 分析旋转方向
        if yaw > 0:
            print(f"  方向: 从朝向墙面转向右侧 (+X方向)")
            print(f"  俯视效果: 投影仪逆时针旋转")
        elif yaw < 0:
            print(f"  方向: 从朝向墙面转向左侧 (-X方向)")
            print(f"  俯视效果: 投影仪顺时针旋转")
        else:
            print(f"  方向: 直接朝向墙面")
        
        # 计算在墙面上的投影中心偏移
        if abs(new_direction[2]) > 1e-6:
            # 投影仪到墙面的射线
            D = 1.5  # 投影仪到墙面距离
            t = D / new_direction[2]
            wall_center = np.array([0, 0, -D]) + new_direction * t
            print(f"  墙面投影中心: ({wall_center[0]:+6.3f}, {wall_center[1]:+6.3f}, {wall_center[2]:+6.3f})")
        print()
    
    print("结论:")
    print("-" * 40)
    print("• 投影仪 yaw = +2° 表示:")
    print("  - 投影仪朝向从正前方转向右侧")
    print("  - 俯视看是逆时针旋转")
    print("  - 墙面上的投影会向右偏移")
    print("  - 符合右手坐标系Y轴旋转定义")
    print()
    print("• 键盘控制对应:")
    print("  - D键 (yaw+): 投影仪向右转，投影向右偏移")
    print("  - A键 (yaw-): 投影仪向左转，投影向左偏移")

if __name__ == "__main__":
    analyze_projector_yaw()
