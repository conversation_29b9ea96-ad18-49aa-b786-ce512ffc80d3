import cv2
import numpy as np

def test_camera_effects():
    """
    测试camera视角效果的简单演示
    """
    print("Testing camera view effects...")
    
    # 创建测试图像
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加网格
    for i in range(0, 640, 40):
        cv2.line(img, (i, 0), (i, 480), (100, 100, 100), 1)
    for i in range(0, 480, 40):
        cv2.line(img, (0, i), (640, i), (100, 100, 100), 1)
    
    # 添加中心标记
    cv2.circle(img, (320, 240), 20, (0, 255, 0), -1)
    cv2.putText(img, "CENTER", (280, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    # 添加角点标记
    corners = [(50, 50), (590, 50), (590, 430), (50, 430)]
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
    labels = ["TL", "TR", "BR", "BL"]
    
    for i, (corner, color, label) in enumerate(zip(corners, colors, labels)):
        cv2.circle(img, corner, 15, color, -1)
        cv2.putText(img, label, (corner[0]-10, corner[1]-20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    # 保存测试图像
    cv2.imwrite("test_pattern.png", img)
    print("Test pattern saved as 'test_pattern.png'")
    print("You can use this image to test the camera view effects in the main program.")
    
    # 显示图像
    cv2.namedWindow("Test Pattern", cv2.WINDOW_NORMAL)
    cv2.imshow("Test Pattern", img)
    cv2.waitKey(3000)  # 显示3秒
    cv2.destroyAllWindows()

if __name__ == "__main__":
    test_camera_effects()
