#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import cv2
import os
import glob
import re
import sys

def main():
    print("开始执行...")
    
    # 配置
    input_dir = r"D:\img\rgb"
    output_video = "output.mp4"
    fps = 15
    
    print(f"输入目录: {input_dir}")
    
    # 检查目录
    if not os.path.exists(input_dir):
        print(f"目录不存在: {input_dir}")
        return
    
    print("目录存在，查找图片文件...")
    
    # 查找图片
    patterns = [
        os.path.join(input_dir, "*.jpg"),
        os.path.join(input_dir, "*.jpeg"),
        os.path.join(input_dir, "*.png"),
        os.path.join(input_dir, "*.bmp"),
        os.path.join(input_dir, "*.JPG"),
        os.path.join(input_dir, "*.JPEG"),
        os.path.join(input_dir, "*.PNG"),
        os.path.join(input_dir, "*.BMP"),
    ]
    
    image_files = []
    for pattern in patterns:
        files = glob.glob(pattern)
        image_files.extend(files)
    
    print(f"找到 {len(image_files)} 个图片文件")
    
    if len(image_files) == 0:
        print("没有找到图片文件")
        # 列出目录中的所有文件
        try:
            all_files = os.listdir(input_dir)
            print(f"目录中的所有文件 ({len(all_files)} 个):")
            for f in all_files[:10]:  # 只显示前10个
                print(f"  {f}")
            if len(all_files) > 10:
                print(f"  ... 还有 {len(all_files) - 10} 个文件")
        except Exception as e:
            print(f"无法列出目录内容: {e}")
        return
    
    # 排序
    def get_number(filename):
        basename = os.path.basename(filename)
        numbers = re.findall(r'\d+', basename)
        return int(numbers[0]) if numbers else 0
    
    image_files.sort(key=get_number)
    
    print("排序后的前几个文件:")
    for i, f in enumerate(image_files[:5]):
        print(f"  {os.path.basename(f)}")
    
    # 读取第一张图片
    print("读取第一张图片...")
    first_img = cv2.imread(image_files[0])
    if first_img is None:
        print(f"无法读取: {image_files[0]}")
        return
    
    h, w = first_img.shape[:2]
    print(f"图片尺寸: {w} x {h}")
    
    # 创建视频写入器
    print("创建视频写入器...")
    fourcc = cv2.VideoWriter_fourcc(*'XVID')  # 尝试XVID编码
    writer = cv2.VideoWriter(output_video, fourcc, fps, (w, h))
    
    if not writer.isOpened():
        print("视频写入器创建失败，尝试其他编码...")
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writer = cv2.VideoWriter(output_video, fourcc, fps, (w, h))
        
        if not writer.isOpened():
            print("视频写入器创建失败")
            return
    
    print("开始写入视频帧...")
    
    # 写入帧
    for i, img_path in enumerate(image_files):
        img = cv2.imread(img_path)
        if img is None:
            print(f"跳过: {os.path.basename(img_path)}")
            continue
        
        # 调整尺寸
        if img.shape[:2] != (h, w):
            img = cv2.resize(img, (w, h))
        
        writer.write(img)
        
        if (i + 1) % 10 == 0:
            print(f"已处理: {i + 1}/{len(image_files)}")
    
    writer.release()
    
    # 检查结果
    if os.path.exists(output_video):
        size_mb = os.path.getsize(output_video) / (1024 * 1024)
        print(f"视频生成成功: {output_video}")
        print(f"文件大小: {size_mb:.2f} MB")
        print(f"总帧数: {len(image_files)}")
        print(f"时长: {len(image_files) / fps:.2f} 秒")
    else:
        print("视频生成失败")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("程序结束")
