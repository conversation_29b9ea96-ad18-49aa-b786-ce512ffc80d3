import numpy as np
import matplotlib.pyplot as plt
import math
# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False 
import matplotlib.pyplot as plt
import numpy as np
import math

# 设置参数
L = 10.0  # 投影距离（单位：米，任意选择，方便画图）
theta_deg = 34.0  # 仰角（度）
width_ratio = 1920 / 1080  # 分辨率宽高比（16:9）

# 计算图像高度 H 和宽度 W
theta_rad = math.radians(theta_deg)
H = L * math.tan(theta_rad)  # 图像高度
W = width_ratio * H  # 图像宽度

# 图像矩形坐标（左下角、右下角、左上角、右上角）
x1 = L - W / 2  # 左下角x
y1 = 0            # 左下角y（与投影仪同高）
x2 = L + W / 2    # 右下角x
y2 = 0            # 右下角y
x3 = L - W / 2    # 左上角x
y3 = H            # 左上角y
x4 = L + W / 2    # 右上角x
y4 = H            # 右上角y

# 创建图形
fig, ax = plt.subplots(figsize=(10, 6))

# 画墙面（垂直线x=L）
ax.axvline(x=L, color='k', linestyle='-', linewidth=2, label='Wall')

# 画投影图像（矩形）
image_rect = plt.Rectangle((x1, y1), W, H, fill=False, color='red', linewidth=2, label='Projected Image')
ax.add_patch(image_rect)

# 画投影仪位置（点）
ax.plot(0, 0, 'ro', markersize=8, label='Projector')

# 画仰角线（从投影仪到上边缘）
ax.plot([0, L], [0, H], 'g--', linewidth=2, label='34° Angle')

# 设置坐标轴和标签
ax.set_xlim(0, L + W / 2 + 1)
ax.set_ylim(-0.5, H + 1)
ax.set_xlabel('Horizontal Distance (x)', fontsize=12)
ax.set_ylabel('Vertical Height (y)', fontsize=12)
ax.set_title('Projection Image on Wall (Resolution: 1920x1080)', fontsize=14)

# 添加网格和图例
ax.grid(True, linestyle='--', alpha=0.7)
ax.legend(loc='upper left')

# 显示图形
plt.tight_layout()
plt.show()