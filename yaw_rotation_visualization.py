"""
详细解释Y轴旋转的俯视效果
"""
import numpy as np
import math

def visualize_yaw_rotation():
    print("Y轴旋转的俯视效果详解")
    print("=" * 60)
    
    print("坐标系设置:")
    print("• X轴: 水平向右 →")
    print("• Y轴: 垂直向上 ↑ (俯视时指向纸面外)")
    print("• Z轴: 深度向前 ↗ (朝向墙面)")
    print()
    
    print("俯视图 (从上往下看，Y轴指向纸面外):")
    print("     Z↗")
    print("     |")
    print("     |")
    print("     •────→ X")
    print("   (原点)")
    print()
    
    print("右手定则验证:")
    print("• 右手四指从+X轴方向弯向+Z轴方向")
    print("• 拇指指向+Y轴方向（向上，俯视时指向纸面外）")
    print("• 这个弯曲方向就是正角度旋转方向")
    print()
    
    print("具体分析:")
    print("-" * 40)
    
    # 分析几个关键点
    angles = [0, 30, 60, 90]
    
    print("从+X轴开始，绕Y轴正向旋转:")
    for angle in angles:
        rad = math.radians(angle)
        # 点(1,0,0)绕Y轴旋转后的位置
        x_new = math.cos(rad) * 1 + math.sin(rad) * 0  # cos(θ)*x + sin(θ)*z
        z_new = -math.sin(rad) * 1 + math.cos(rad) * 0  # -sin(θ)*x + cos(θ)*z
        
        print(f"  {angle:2d}°: (+X轴上的点) → ({x_new:+5.2f}, 0, {z_new:+5.2f})")
        
        if angle == 0:
            print("       位置: X轴正方向")
        elif angle == 30:
            print("       位置: 从X轴向Z轴偏转")
        elif angle == 60:
            print("       位置: 更接近Z轴")
        elif angle == 90:
            print("       位置: Z轴正方向")
    
    print()
    print("俯视图中的旋转轨迹:")
    print("     Z↗")
    print("     |  ↙ 90°")
    print("     | ↙ 60°")
    print("     |↙ 30°")
    print("     •────→ X (0°)")
    print("   (原点)")
    print()
    print("可以看出，从+X向+Z的旋转在俯视图中是逆时针的！")
    print()
    
    print("投影仪朝向分析:")
    print("-" * 40)
    print("• 投影仪初始朝向: +Z方向 (朝向墙面)")
    print("• yaw = +2°时: 朝向从+Z偏向+X")
    print("• 这意味着投影仪'转头'看向右侧")
    print("• 在俯视图中，这个'转头'动作是逆时针的")
    print()
    
    print("为什么会觉得反直觉？")
    print("-" * 40)
    print("• 日常生活中，我们习惯说'向右转'是顺时针")
    print("• 但那是基于人的视角，面向前方时的'右转'")
    print("• 在数学坐标系中，绕Y轴的正向旋转定义是固定的")
    print("• 从+X向+Z的旋转，俯视确实是逆时针")
    print()
    
    print("验证方法:")
    print("-" * 40)
    print("1. 伸出右手，拇指向上(+Y)")
    print("2. 四指从右侧(+X)弯向前方(+Z)")
    print("3. 这个弯曲方向就是正向旋转")
    print("4. 俯视这个动作，确实是逆时针的")

if __name__ == "__main__":
    visualize_yaw_rotation()
