import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle

plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False 
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon

def chessboard_projection(yaw_deg, pitch_deg, d=1.0, f=500.0, img_size=(640, 480)):
    """
    生成棋盘格在摄像头中的成像效果
    
    参数:
    yaw_deg: 左右转动角度 (度)，正数表示向右转
    pitch_deg: 上下旋转角度 (度)，正数表示抬头（向上）
    d: 摄像头到棋盘格的距离 (默认 1.0 米)
    f: 相机焦距 (默认 500 像素)
    img_size: 输出图像尺寸 (默认 (640, 480))
    
    输出: 显示棋盘格投影的图像
    """
    # 定义棋盘格四个角点 (世界坐标系, z=0 平面, 单位: 米)
    # 棋盘格尺寸为 1x1 米, 中心在 (0,0,0)
    chessboard_points_world = np.array([
        [0.5, 0.5, 0],   # 右上角
        [0.5, -0.5, 0],  # 右下角
        [-0.5, -0.5, 0], # 左下角
        [-0.5, 0.5, 0]   # 左上角
    ])
    
    # 转换为弧度
    yaw = np.radians(yaw_deg)
    pitch = np.radians(pitch_deg)
    
    # 1. 计算旋转矩阵 (先 pitch 再 yaw)
    R_pitch = np.array([
        [np.cos(pitch), 0, np.sin(pitch)],
        [0, 1, 0],
        [-np.sin(pitch), 0, np.cos(pitch)]
    ])
    
    R_yaw = np.array([
        [np.cos(yaw), -np.sin(yaw), 0],
        [np.sin(yaw), np.cos(yaw), 0],
        [0, 0, 1]
    ])
    
    R = R_yaw @ R_pitch  # 旋转矩阵 = R_yaw * R_pitch
    
    # 2. 计算每个角点的相机坐标系位置
    points_cam = []
    for point in chessboard_points_world:
        # 世界坐标系到摄像头的向量: (x, y, z - d) = (x, y, -d)
        rel = point - np.array([0, 0, d])
        # 应用旋转: 相机坐标系 = R * 世界坐标系向量
        cam_point = R @ rel
        points_cam.append(cam_point)
    
    points_cam = np.array(points_cam)
    
    # 3. 投影到图像平面 (处理 z<0 的点, 仅保留前方可见点)
    u_list, v_list = [], []
    for p in points_cam:
        z_cam = p[2]
        if z_cam >= 0:  # 点在相机后方, 不可见
            continue
            
        # 深度 = -z_cam (正数, 表示到相机的距离)
        depth = -z_cam
        # 相机坐标系 (x右, y上, z前) -> 图像坐标系 (u右, v下)
        u = f * p[0] / depth
        v = f * (-p[1]) / depth  # y轴反转 (相机y上 -> 图像v下)
        u_list.append(u)
        v_list.append(v)
    
    # 4. 转换为图像坐标 (以图像中心为原点, 再偏移)
    u_img = [u + img_size[0] / 2 for u in u_list]
    v_img = [v + img_size[1] / 2 for v in v_list]
    
    # 5. 绘制图像
    plt.figure(figsize=(8, 6))
    plt.xlim(0, img_size[0])
    plt.ylim(0, img_size[1])
    plt.gca().invert_yaxis()  # 修正 y 轴方向 (图像坐标 v 向下)
    
    # 绘制棋盘格边界
    if u_list:  # 有可见点
        plt.plot(u_img, v_img, 'b-', linewidth=2)
        plt.plot(u_img, v_img, 'bo', markersize=5)
        plt.title(f'Chessboard Projection (yaw={yaw_deg}°, pitch={pitch_deg}°)')
    else:
        plt.text(img_size[0]/2, img_size[1]/2, 'No visible points', 
                 ha='center', va='center', fontsize=14)
    
    plt.xlabel('Image Width (pixels)')
    plt.ylabel('Image Height (pixels)')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.show()

# ============== 使用示例 ==============
if __name__ == "__main__":
    # 示例 1: 初始位置 (无旋转)
    print("Example 1: Initial position (yaw=0°, pitch=0°)")
    chessboard_projection(0, 0)
    
    # 示例 2: 向右转 30° (yaw=30°), 无上下旋转
    print("\nExample 2: Right turn (yaw=30°, pitch=0°)")
    chessboard_projection(30, 0)
    
    # # 示例 3: 抬头 20° (pitch=20°), 无左右旋转
    # print("\nExample 3: Pitch up (yaw=0°, pitch=20°)")
    # chessboard_projection(0, 20)
    
    # # 示例 4: 复杂旋转 (yaw=45°, pitch=15°)
    # print("\nExample 4: Combined rotation (yaw=45°, pitch=15°)")
    # chessboard_projection(45, 15)