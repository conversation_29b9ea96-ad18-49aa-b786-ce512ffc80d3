import numpy as np
from scipy.optimize import least_squares
import math

# 假设我们有测量数据（实际应用中需要替换为你的数据）
# x_data: 测量的x值
# d_data: 测量的d值
x_data = np.array([572.767,  614.03], dtype=np.float32)  # 示例数据  # 580, 587, 592, 596, 600, 603, 606, 609, 612,
#d_data = np.array([1, 1.1, 1.2, 1.3,])  
d_data = np.arange(10, 21, 10).astype(np.float32) / 10    # 示例数据
print(x_data, d_data)
# 定义残差函数
def residual(params, x_data, d_data):
    xc,  f = params
    a, gamma = 0.1, 2
    # 为a添加小值避免除以零
    a_safe = a 
    # 使用平滑近似处理绝对值（避免不可微点）
    abs_approx = (xc - x_data)
    # 计算残差
    residuals = abs_approx - f * (np.tan(np.deg2rad(90 - np.arctan(d_data / a_safe) * 180 / np.pi - gamma)))
    return residuals

# 选择初始猜测值（根据你的经验或数据统计）
# 例如：xc可以取x_data的中位数，a可以取d_data的平均值，gamma可以基于其他参数估算
initial_guess = [633, 864]  # 示例初始值

print( (1080 / np.tan(np.deg2rad(34))) * np.tan(np.deg2rad(17)))

for i in range(len(x_data)):
    print(np.arctan(d_data[i] / 0.1) * 180 / np.pi, (633 - x_data[i]) / np.tan(np.deg2rad(90 - np.arctan(d_data[i] / 0.1) * 180 / np.pi - 2)) )
# 使用最小二乘法求解
result = least_squares(residual, initial_guess, args=(x_data, d_data))

# 提取求解结果
xc_opt, f_opt = result.x

print("优化后的参数:")
print(f"xc = {xc_opt:.4f}")

print(f"f = {f_opt:.4f}")
# 验证结果
residuals = residual(result.x, x_data, d_data)
print(f"\n残差平方和: {np.sum(residuals**2):.4f}")
print(f"平均残差: {np.mean(np.abs(residuals)):.4f}")

d_pred = np.arange(10, 21, 1).astype(np.float32) / 10 
x_pred = np.array([572.76733, 580.1961, 586.15784, 591.2436, 596.0168, 600.1633, 603.403, 606.16846, 609.1837, 612.026, 614.0285], dtype=np.float32)
for i, x in enumerate(x_pred):
    print(np.tan(np.deg2rad(90 - 2 - np.arctan((xc_opt - x) / f_opt) * 180 / np.pi)) * 0.1, d_pred[i])