"""
详细分析Camera和投影仪的几何关系
"""
import numpy as np
import math

def rot_y(deg):
    """Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def analyze_geometry():
    print("Camera-投影仪几何关系分析")
    print("=" * 60)
    
    # 基本设置
    D_m = 1.5  # 投影仪到墙面距离
    dx_cam = 0.1  # camera相对投影仪的X方向距离
    
    print("初始设置:")
    print(f"• 投影仪位置: (0, 0, {-D_m})")
    print(f"• Camera位置: ({dx_cam}, 0, {-D_m}) - 在投影仪右侧")
    print(f"• 墙面位置: Z = 0")
    print()
    
    print("关键问题分析:")
    print("-" * 40)
    print("问题: 为什么投影仪和camera的yaw都是+2°，但方向看起来不同？")
    print()
    
    # 分析投影仪yaw = +2°
    print("1. 投影仪 yaw = +2°:")
    proj_initial = np.array([0, 0, 1])  # 朝向+Z
    R_proj = rot_y(2.0)
    proj_new = R_proj @ proj_initial
    print(f"   朝向变化: {proj_initial} → ({proj_new[0]:+.3f}, {proj_new[1]:+.3f}, {proj_new[2]:+.3f})")
    print(f"   含义: 从朝向墙面(+Z) 转向 右侧(+X)")
    print()
    
    # 分析camera yaw = +2°
    print("2. Camera yaw = +2° (相对于投影仪):")
    cam_initial = np.array([0, 0, 1])  # 朝向+Z
    R_cam = rot_y(2.0)
    cam_new = R_cam @ cam_initial
    print(f"   朝向变化: {cam_initial} → ({cam_new[0]:+.3f}, {cam_new[1]:+.3f}, {cam_new[2]:+.3f})")
    print(f"   含义: 从朝向墙面(+Z) 转向 右侧(+X)")
    print()
    
    print("等等！这里有个关键点:")
    print("-" * 40)
    print("Camera的yaw = +2°是相对于投影仪坐标系的！")
    print()
    
    # 分析camera相对于投影仪的方向
    print("3. Camera相对投影仪的几何关系:")
    proj_pos = np.array([0, 0, -D_m])
    cam_pos = np.array([dx_cam, 0, -D_m])
    
    # Camera朝向投影仪的方向向量
    cam_to_proj = proj_pos - cam_pos
    cam_to_proj_normalized = cam_to_proj / np.linalg.norm(cam_to_proj)
    
    print(f"   投影仪位置: {proj_pos}")
    print(f"   Camera位置: {cam_pos}")
    print(f"   Camera→投影仪方向: {cam_to_proj_normalized}")
    print(f"   这个方向是 (-1, 0, 0)，即 -X方向")
    print()
    
    print("4. 关键理解:")
    print("-" * 40)
    print("• Camera初始朝向: +Z方向 (朝向墙面)")
    print("• Camera要朝向投影仪: 需要朝向 -X方向")
    print("• 从+Z转向-X需要什么旋转？")
    print()
    
    # 计算从+Z到-X需要的旋转
    target_direction = np.array([-1, 0, 0])  # -X方向
    
    print("5. 从+Z到-X的旋转分析:")
    print("   如果yaw = -90°:")
    R_neg90 = rot_y(-90)
    result_neg90 = R_neg90 @ np.array([0, 0, 1])
    print(f"   结果: ({result_neg90[0]:+.3f}, {result_neg90[1]:+.3f}, {result_neg90[2]:+.3f})")
    print("   这确实是-X方向！")
    print()
    
    print("6. 那么yaw = +2°是什么意思？")
    print("   yaw = +2°: 从+Z稍微转向+X")
    print("   这意味着camera从朝向墙面，稍微转向投影仪方向")
    print("   但只转了2°，还远没有完全朝向投影仪")
    print()
    
    print("结论:")
    print("-" * 40)
    print("• 投影仪yaw = +2°: 朝向从+Z转向+X (向右转)")
    print("• Camera yaw = +2°: 朝向从+Z转向+X (也是向右转)")
    print("• 两者的yaw旋转方向是一致的！")
    print("• Camera的+2°只是朝向投影仪方向的一个小角度")
    print("• 如果要完全朝向投影仪，需要yaw = -90°")
    print()
    print("注释中的'朝向投影仪方向'是指:")
    print("• 从朝向墙面开始，向投影仪方向偏转")
    print("• +2°是一个小的偏转角度，不是完全朝向")

if __name__ == "__main__":
    analyze_geometry()
