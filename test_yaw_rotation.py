"""
测试投影仪yaw旋转时camera成像效果的修复
"""
import cv2
import numpy as np

def create_directional_test_image():
    """创建一个有方向性的测试图像，便于观察旋转效果"""
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 添加背景网格
    for i in range(0, 640, 40):
        cv2.line(img, (i, 0), (i, 480), (30, 30, 30), 1)
    for i in range(0, 480, 40):
        cv2.line(img, (0, i), (640, i), (30, 30, 30), 1)
    
    # 添加大箭头指向右侧（X+方向）
    arrow_points = np.array([
        [200, 240],  # 箭头尾部左
        [400, 240],  # 箭头身体右
        [400, 200],  # 箭头头部上
        [480, 240],  # 箭头尖端
        [400, 280],  # 箭头头部下
        [400, 240],  # 箭头身体右
        [200, 240]   # 回到起点
    ], np.int32)
    
    cv2.fillPoly(img, [arrow_points], (0, 255, 0))
    cv2.putText(img, "YAW TEST", (250, 320), cv2.FONT_HERSHEY_SIMPLEX, 1.2, (255, 255, 255), 3)
    cv2.putText(img, "RIGHT ->", (480, 250), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    
    # 添加角点标记，便于观察变形
    corners = [(50, 50), (590, 50), (590, 430), (50, 430)]
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]
    labels = ["TL", "TR", "BR", "BL"]
    
    for corner, color, label in zip(corners, colors, labels):
        cv2.circle(img, corner, 12, color, -1)
        cv2.putText(img, label, (corner[0]-15, corner[1]-25), cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    
    # 添加中心十字标记
    cv2.line(img, (310, 240), (330, 240), (255, 255, 255), 3)
    cv2.line(img, (320, 230), (320, 250), (255, 255, 255), 3)
    cv2.putText(img, "CENTER", (270, 380), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    return img

def main():
    print("Yaw Rotation Test Pattern Generator")
    print("=" * 50)
    
    # 创建测试图像
    test_img = create_directional_test_image()
    cv2.imwrite("yaw_test_pattern.png", test_img)
    
    print("✓ Test pattern created: 'yaw_test_pattern.png'")
    print("\nTo test the yaw rotation fix:")
    print("1. Run: python cz_projector_simulate_keyboard.py")
    print("2. Make sure camera is in 'Following' mode (press 'C' if needed)")
    print("3. Use A/D keys to rotate projector yaw left/right")
    print("4. Observe the camera view:")
    print("   - The green arrow should rotate correctly")
    print("   - Corner markers should move as expected")
    print("   - No strange distortions or inversions")
    print("\nExpected behavior:")
    print("- A key (yaw left): Arrow rotates counterclockwise in camera view")
    print("- D key (yaw right): Arrow rotates clockwise in camera view")
    print("- Camera view should match the geometric relationship")
    
    # 显示测试图像
    cv2.namedWindow("Yaw Test Pattern", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Yaw Test Pattern", 640, 480)
    cv2.imshow("Yaw Test Pattern", test_img)
    print("\nPress any key to close the preview...")
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    print("\n" + "=" * 50)
    print("Test pattern ready! Use it to verify yaw rotation fix.")

if __name__ == "__main__":
    main()
