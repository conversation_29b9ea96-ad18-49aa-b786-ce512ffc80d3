import cv2
import numpy as np
import math

# 简单测试camera视角功能
def test_camera_view():
    # 创建一个简单的测试图像
    img = np.zeros((480, 640, 3), dtype=np.uint8)
    cv2.rectangle(img, (100, 100), (540, 380), (255, 255, 255), 2)
    cv2.circle(img, (320, 240), 50, (0, 255, 0), -1)
    cv2.putText(img, "TEST", (280, 250), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 0, 0), 2)
    
    # 创建camera视角图像（简单复制）
    camera_img = img.copy()
    cv2.putText(camera_img, "Camera View", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)
    
    # 显示两个窗口
    cv2.namedWindow("Projector View", cv2.WINDOW_NORMAL)
    cv2.namedWindow("Camera View", cv2.WINDOW_NORMAL)
    
    while True:
        cv2.imshow("Projector View", img)
        cv2.imshow("Camera View", camera_img)
        
        key = cv2.waitKey(30) & 0xFF
        if key == 27:  # ESC
            break
    
    cv2.destroyAllWindows()

if __name__ == "__main__":
    test_camera_view()
