"""
正确分析Y轴旋转方向
"""
import numpy as np
import math

def correct_rotation_analysis():
    print("Y轴旋转方向的正确分析")
    print("=" * 50)
    
    print("问题分析:")
    print("• 投影仪朝向从 +Z 转向 +X")
    print("• 右手定则：四指从 +X 弯向 +Z")
    print("• 这两个方向是相反的！")
    print()
    
    print("正确的理解:")
    print("-" * 30)
    
    # Y轴旋转矩阵
    def rot_y(deg):
        a = math.radians(deg)
        ca, sa = math.cos(a), math.sin(a)
        return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)
    
    # 分析投影仪朝向变化
    initial_direction = np.array([0, 0, 1])  # +Z方向
    
    print("投影仪朝向变化:")
    for yaw in [-2, 0, 2]:
        R = rot_y(yaw)
        new_direction = R @ initial_direction
        print(f"yaw = {yaw:+2d}°: 朝向 ({new_direction[0]:+5.2f}, {new_direction[1]:+5.2f}, {new_direction[2]:+5.2f})")
    
    print()
    print("关键发现:")
    print("• yaw = +2°: 朝向变为 (+0.03, 0, +0.99)")
    print("• 这意味着朝向从 +Z 偏向 +X")
    print("• 但这与右手定则 (+X → +Z) 方向相反！")
    print()
    
    print("矛盾的根源:")
    print("-" * 30)
    print("1. 右手定则定义的是旋转轴和旋转方向的关系")
    print("2. 但我们关心的是物体朝向的变化")
    print("3. 物体的朝向变化与坐标轴的旋转方向是相反的")
    print()
    
    print("正确的解释:")
    print("-" * 30)
    print("• 右手定则：绕+Y轴正向旋转，坐标系从+X转向+Z")
    print("• 物体朝向：在这个旋转中，朝向从+Z转向+X")
    print("• 这两个是相反的，因为我们看的是朝向向量的变化")
    print()
    
    print("俯视图分析:")
    print("-" * 30)
    print("初始状态:")
    print("     Z↑")
    print("     │")
    print("     │ ← 投影仪朝向")
    print("     │")
    print("     •────→ X")
    print()
    
    print("yaw = +2° 后:")
    print("     Z↑")
    print("     │")
    print("     │")
    print("     │  ↗ ← 投影仪朝向")
    print("     •────→ X")
    print()
    
    print("朝向变化轨迹:")
    print("从 ↑ 变为 ↗，这在俯视图中确实是顺时针的！")
    print()
    
    print("结论:")
    print("-" * 30)
    print("• 投影仪 yaw = +2° 表示朝向从+Z转向+X")
    print("• 在俯视图中，这个朝向变化是顺时针的")
    print("• 之前说'逆时针'是错误的")
    print("• 正确的描述：投影仪向右转，俯视效果是顺时针")

if __name__ == "__main__":
    correct_rotation_analysis()
