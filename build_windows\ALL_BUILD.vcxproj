﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="16.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{822EC809-0E89-3645-8943-483336CDC23D}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v142</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>D:\Program\Project\project\czcv_camera_new\third_party\source\rapidjson-1.1.0\include;D:\Program\Project\project\czcv_camera_new\third_party\source\Eigen\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\ncnn_20220216\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\glog\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\tnn\include;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\Eigen\include;D:\Program\Project\project\czcv_camera_new\.\lib\include;D:\Program\Project\project\czcv_camera_new\.\lib\src;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\Program\Project\project\czcv_camera_new\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCXXCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeRCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeSystem.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig-version.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules-release.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules.cmake;D:\Program\Project\project\czcv_camera_new\third_party\third_party.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCXXCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeRCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeSystem.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig-version.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules-release.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules.cmake;D:\Program\Project\project\czcv_camera_new\third_party\third_party.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCXXCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeRCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeSystem.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig-version.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules-release.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules.cmake;D:\Program\Project\project\czcv_camera_new\third_party\third_party.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule D:/Program/Project/project/czcv_camera_new/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"D:\Program Files\CMake\bin\cmake.exe" -SD:/Program/Project/project/czcv_camera_new -BD:/Program/Project/project/czcv_camera_new/build_windows --check-stamp-file D:/Program/Project/project/czcv_camera_new/build_windows/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCXXInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeCommonLanguageInclude.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeGenericSystem.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeInitializeConfigs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeLanguageInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeRCInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInformation.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\CMakeSystemSpecificInitialize.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\CMakeCommonCompilerMacros.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Compiler\MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageHandleStandardArgs.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\FindPackageMessage.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-C.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC-CXX.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows-MSVC.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\Windows.cmake;D:\Program Files\CMake\share\cmake-3.25\Modules\Platform\WindowsPaths.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeCXXCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeRCCompiler.cmake;D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\3.25.3\CMakeSystem.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig-version.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVConfig.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules-release.cmake;D:\Program\Project\project\czcv_camera_new\third_party\prebuilt\windows\opencv4.5.1\x64\vc16\staticlib\OpenCVModules.cmake;D:\Program\Project\project\czcv_camera_new\third_party\third_party.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">D:\Program\Project\project\czcv_camera_new\build_windows\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="D:\Program\Project\project\czcv_camera_new\build_windows\ZERO_CHECK.vcxproj">
      <Project>{74EF2365-105C-36A3-AD33-0DBDF5B4175D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\Program\Project\project\czcv_camera_new\build_windows\lib\czcv_camera.vcxproj">
      <Project>{DB4F4ABB-664A-3BD6-A3AD-AC9AE6DAB088}</Project>
      <Name>czcv_camera</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Program\Project\project\czcv_camera_new\build_windows\test\simulate_center_stage.vcxproj">
      <Project>{2F2743E8-CBCF-3D1F-A93A-1B823651AE6C}</Project>
      <Name>simulate_center_stage</Name>
    </ProjectReference>
    <ProjectReference Include="D:\Program\Project\project\czcv_camera_new\build_windows\test\test_center_stage.vcxproj">
      <Project>{4A05F871-9D8E-3871-A8CB-6F1DC586679C}</Project>
      <Name>test_center_stage</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>