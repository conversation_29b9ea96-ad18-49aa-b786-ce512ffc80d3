"""
重新验证camera朝向投影仪的旋转方向
"""
import numpy as np
import math

def rot_y(deg):
    """Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def verify_camera_direction():
    print("重新验证Camera朝向投影仪的旋转方向")
    print("=" * 50)
    
    # 位置设置
    D_m = 1.5
    dx_cam = 0.1
    
    proj_pos = np.array([0, 0, -D_m])      # 投影仪位置
    cam_pos = np.array([dx_cam, 0, -D_m])  # Camera位置
    
    print("位置关系:")
    print(f"投影仪位置: {proj_pos}")
    print(f"Camera位置: {cam_pos}")
    print(f"投影仪X坐标 ({proj_pos[0]}) < Camera X坐标 ({cam_pos[0]})")
    print("确认：投影仪在Camera的左侧")
    print()
    
    # Camera朝向投影仪的方向向量
    cam_to_proj = proj_pos - cam_pos
    print(f"Camera→投影仪的方向向量: {cam_to_proj}")
    print(f"归一化后: {cam_to_proj / np.linalg.norm(cam_to_proj)}")
    print("这是 (-1, 0, 0)，即 -X方向")
    print()
    
    print("旋转分析:")
    print("-" * 30)
    
    # Camera初始朝向
    initial_direction = np.array([0, 0, 1])  # +Z方向
    print(f"Camera初始朝向: {initial_direction} (+Z方向，朝向墙面)")
    
    # 目标朝向
    target_direction = np.array([-1, 0, 0])  # -X方向
    print(f"目标朝向: {target_direction} (-X方向，朝向投影仪)")
    print()
    
    print("测试不同yaw角度:")
    print("-" * 30)
    
    test_angles = [-90, -45, -2, 0, 2, 45, 90]
    
    for yaw in test_angles:
        R = rot_y(yaw)
        new_direction = R @ initial_direction
        
        # 计算与目标方向的夹角
        dot_product = np.dot(new_direction, target_direction)
        angle_to_target = math.degrees(math.acos(np.clip(dot_product, -1, 1)))
        
        print(f"yaw = {yaw:+3d}°: 朝向 ({new_direction[0]:+6.3f}, {new_direction[1]:+6.3f}, {new_direction[2]:+6.3f})")
        print(f"         与投影仪方向夹角: {angle_to_target:5.1f}°")
        
        if abs(angle_to_target) < 0.1:
            print("         ★ 完全朝向投影仪！")
        elif angle_to_target < 90:
            print("         ↗ 朝向投影仪方向偏转")
        print()
    
    print("结论:")
    print("-" * 30)
    print("• Camera完全朝向投影仪需要: yaw = -90°")
    print("• yaw = -2°: 朝向投影仪方向的小角度偏转")
    print("• yaw = +2°: 背离投影仪方向的小角度偏转")
    print()
    print("因此，如果要表示'camera向投影仪方向yaw 2°'")
    print("正确的值应该是: yaw = -2°")

if __name__ == "__main__":
    verify_camera_direction()
