#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试视频生成功能
"""

import cv2
import os
import glob
import re

def test_directory_and_generate_video():
    """测试目录并生成视频"""
    
    # 配置参数
    input_dir = r"D:\img\rgb"
    output_video = "test_output.mp4"
    fps = 15
    
    print("=" * 50)
    print("测试视频生成功能")
    print("=" * 50)
    
    # 1. 检查目录是否存在
    print(f"1. 检查输入目录: {input_dir}")
    if os.path.exists(input_dir):
        print("   ✓ 目录存在")
        
        # 列出目录内容
        try:
            files = os.listdir(input_dir)
            print(f"   目录中有 {len(files)} 个文件")
            
            # 显示前几个文件
            if files:
                print("   前几个文件:")
                for i, f in enumerate(files[:5]):
                    print(f"     {f}")
                if len(files) > 5:
                    print(f"     ... 还有 {len(files) - 5} 个文件")
            else:
                print("   目录为空")
                return False
                
        except Exception as e:
            print(f"   ✗ 无法读取目录内容: {e}")
            return False
    else:
        print("   ✗ 目录不存在")
        return False
    
    # 2. 查找图片文件
    print("\n2. 查找图片文件...")
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif']
    image_files = []
    
    for ext in image_extensions:
        pattern = os.path.join(input_dir, f"*{ext}")
        found_files = glob.glob(pattern)
        image_files.extend(found_files)
        
        pattern = os.path.join(input_dir, f"*{ext.upper()}")
        found_files = glob.glob(pattern)
        image_files.extend(found_files)
    
    if not image_files:
        print("   ✗ 未找到任何图片文件")
        return False
    
    print(f"   ✓ 找到 {len(image_files)} 个图片文件")
    
    # 3. 按数字排序
    print("\n3. 对文件进行排序...")
    def extract_number(filename):
        basename = os.path.basename(filename)
        numbers = re.findall(r'\d+', basename)
        return int(numbers[0]) if numbers else 0
    
    image_files.sort(key=extract_number)
    
    print("   前几个排序后的文件:")
    for i, f in enumerate(image_files[:5]):
        print(f"     {os.path.basename(f)}")
    if len(image_files) > 5:
        print(f"     ... 还有 {len(image_files) - 5} 个文件")
    
    # 4. 检查第一张图片
    print("\n4. 检查图片格式...")
    first_image = cv2.imread(image_files[0])
    if first_image is None:
        print(f"   ✗ 无法读取第一张图片: {image_files[0]}")
        return False
    
    height, width = first_image.shape[:2]
    print(f"   ✓ 图片尺寸: {width} x {height}")
    
    # 5. 生成视频
    print(f"\n5. 生成视频 (帧率: {fps} FPS)...")
    
    # 创建视频编码器
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    video_writer = cv2.VideoWriter(output_video, fourcc, fps, (width, height))
    
    if not video_writer.isOpened():
        print("   ✗ 无法创建视频编码器")
        return False
    
    # 写入视频帧
    for i, image_path in enumerate(image_files):
        img = cv2.imread(image_path)
        if img is None:
            print(f"   警告: 跳过无法读取的图片 {os.path.basename(image_path)}")
            continue
        
        # 确保尺寸一致
        if img.shape[:2] != (height, width):
            img = cv2.resize(img, (width, height))
        
        video_writer.write(img)
        
        # 显示进度
        if (i + 1) % 20 == 0 or i == len(image_files) - 1:
            progress = (i + 1) / len(image_files) * 100
            print(f"   进度: {i + 1}/{len(image_files)} ({progress:.1f}%)")
    
    video_writer.release()
    
    # 6. 检查输出文件
    print(f"\n6. 检查输出文件...")
    if os.path.exists(output_video):
        file_size = os.path.getsize(output_video) / (1024 * 1024)  # MB
        print(f"   ✓ 视频生成成功!")
        print(f"   文件: {output_video}")
        print(f"   大小: {file_size:.2f} MB")
        print(f"   总帧数: {len(image_files)}")
        print(f"   视频时长: {len(image_files) / fps:.2f} 秒")
        return True
    else:
        print("   ✗ 视频文件未生成")
        return False

if __name__ == "__main__":
    success = test_directory_and_generate_video()
    if success:
        print("\n🎉 测试成功!")
    else:
        print("\n❌ 测试失败!")
