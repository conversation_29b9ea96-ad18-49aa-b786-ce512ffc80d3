import json
import math
from dataclasses import dataclass

import cv2
import numpy as np

from scipy.optimize import least_squares


def get_corners_from_left_top(width, height, square_w, square_h, squares_x, squares_y):
    """
    按 从上到下、每行从左到右 的顺序生成角点坐标。
    这里角点是所有网格交点，因此有 (squares_x+1) * (squares_y+1) 个角点。
    """
    corners = []
    for j in range(squares_y + 1):         # 从上到下
        for i in range(squares_x + 1):     # 每行从左到右
            x = i * square_w
            y = j * square_h
            # 防止落在 width / height 边界之外（最后一个点可能等于 width / height）
            x = min(x, width - 1)
            y = min(y, height - 1)
            corners.append((x, y))
    return corners

def get_proj_pts():
    width, height = 1920, 1080
    squares_x, squares_y = 12, 8
    square_w = width // squares_x
    square_h = height // squares_y

    # 生成角点（从左到右，从上到下）
    corners = get_corners_from_left_top(
        width, height,
        square_w, square_h,
        squares_x, squares_y
    )
    corners = np.array(corners).reshape(squares_y + 1, squares_x + 1, 2)
    corners = corners[1:-1, 1:-1, :].reshape(-1, 2)
    return corners

# ---------- 旋转工具 ----------
def rot_x(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)

def rot_y(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def rot_z(deg: float) -> np.ndarray:
    a = math.radians(deg); ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    # camera-to-world rotation
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)


def  get_point(fn='data/cam_720p_ang0.json'):
    j = json.load(open(fn))
    p_list = []
    for s in j['shapes']:
        p = s['points'][0]
        p_list.append(p)
    return p_list

def get_cam_K():
    # return np.array(
    #     [[813.13991884, 0,             573.13396352],
    #      [0,            822.02481376,  408.75756843],
    #      [0, 0, 1],
    #      ])
    return  np.array(
        [[838.295, 0,        618.496],
         [0,       851.146,  403.593],
         [0,       0,        1],
        ])

# ---------- 内参 ----------
def intrinsics_from_throw_ratio(w, h, throw_ratio, cx, cy):
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))  # rad
    fx = (w / 2.0) / math.tan(fovx / 2.0)
    fovy = 2.0 * math.atan(math.tan(fovx / 2.0) * (h / w))
    fy = (h / 2.0) / math.tan(fovy / 2.0)

    return np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)


def get_projector_K():
    Wp, Hp = 1920, 1080
    throw_ratio = 0.8
    cxp, cyp = 906, Hp
    Kp = intrinsics_from_throw_ratio(Wp, Hp, throw_ratio, cxp, cyp)
    return Kp


# ---------- 从投影仪像素 -> 墙面 3D ----------
def projector_pixel_to_world(u, v, Kp, D):
    """
    u, v: (N,) 投影仪像素坐标
    D: 投影中心到墙面的距离 (m)
    返回: (N,3) 世界坐标点 (X,Y,0)
    """
    uv1 = np.vstack([u, v, np.ones_like(u, dtype=np.float64)])  # (3,N)
    Kinv = np.linalg.inv(Kp)
    d = Kinv @ uv1      # (3,N)
    dx, dy, dz = d

    Cp = np.array([0.0, 0.0, -D], dtype=np.float64).reshape(3,1)

    t = D / dz
    P = Cp + d * t      # (3,N)
    return P.T          # (N,3)

# ---------- 从世界 3D -> 相机像素 ----------
def world_to_camera_pixel(P_w, Kc, dx, D, pitch, yaw, roll):
    """
    P_w: (N,3) 世界坐标点
    dx: 相机中心相对投影仪在 X 方向上的基线 (m)
    D:  与墙面的距离
    返回: (N,2) 相机像素坐标
    """
    # 相机中心
    C_cam = np.array([dx, 0.0, -D], dtype=np.float64).reshape(3,1)

    # camera-to-world
    R_c2w = build_rotation(pitch, yaw, roll)
    # world-to-camera
    R_w2c = R_c2w.T

    Pw = P_w.T                   # (3,N)
    Pc = R_w2c @ (Pw - C_cam)    # (3,N)
    Xc, Yc, Zc = Pc

    eps = 1e-9
    Zc = np.where(Zc < eps, eps, Zc)

    fx, fy = Kc[0,0], Kc[1,1]
    cx, cy = Kc[0,2], Kc[1,2]

    u = fx * (Xc / Zc) + cx
    v = fy * (Yc / Zc) + cy
    return np.vstack([u, v]).T   # (N,2)

# ---------- 只存“固定参数”：内参 + 基线 ----------
@dataclass
class SystemFixed:
    Kp: np.ndarray      # 投影仪内参
    Kc: np.ndarray      # 相机内参
    dx: float           # 相机相对投影仪在 X 方向上的基线 (米)


# ---------- 残差函数：同时优化角度 + D ----------
def residual_params(theta, fixed: SystemFixed, proj_pts, cam_obs):
    """
    theta: [pitch, yaw, roll, D]
    fixed: 包含 Kp, Kc, dx
    proj_pts: (N,2)
    cam_obs:  (N,2)
    返回 (2N,) 残差
    """
    pitch, yaw, roll, D = theta
    #D = 2.0
    Kp, Kc, dx = fixed.Kp, fixed.Kc, fixed.dx

    u_p = proj_pts[:,0]
    v_p = proj_pts[:,1]

    # 投影仪像素 -> 墙面 3D
    Pw = projector_pixel_to_world(u_p, v_p, Kp, D)  # (N,3)

    # 墙面 3D -> 相机像素
    cam_pred = world_to_camera_pixel(Pw, Kc, dx, D, pitch, yaw, roll)  # (N,2)

    res = (cam_pred - cam_obs).ravel()
    return res


if __name__ == '__main__':

    proj_pts = get_proj_pts()
    N = proj_pts.shape[0]
    print("Number of chessboard points:", N)


    Kp = get_projector_K()
    Kc = get_cam_K()
    dx = 0.1


    image = cv2.imread('./data/cam_720p_ang0.jpg')
    cam_obs = get_point(fn='data/cam_720p_ang0.json')

    if True:
        # proj_pts  和 cam_obs 求一个 H 矩阵， 看下能到的误差为多少
        proj_pts_np = proj_pts.astype(np.float32)  # (N,2), 投影像素
        cam_obs_np = np.asarray(cam_obs, dtype=np.float32)  # (N,2), 相机像素

        # 检查一下数量是否一致
        assert proj_pts_np.shape[0] == cam_obs_np.shape[0], "proj_pts 和 cam_obs 数量不一致"

        # 不用 RANSAC，直接最小二乘：method=0
        H, mask = cv2.findHomography(proj_pts_np, cam_obs_np, method=0)

        print("H (projector -> camera):")
        print(H)

        proj_pts_reshaped = proj_pts_np.reshape(-1, 1, 2)  # (N,1,2)
        cam_pred = cv2.perspectiveTransform(proj_pts_reshaped, H).reshape(-1, 2)  # (N,2)

        # 每个点的像素误差
        reproj_err = np.linalg.norm(cam_pred - cam_obs_np, axis=1)

        print("单 H 映射能做到的极限误差: ")
        print("  mean = ", reproj_err.mean())
        print("  std  = ", reproj_err.std())
        print("  max  = ", reproj_err.max())
        print('==='*20)



    # for i, (x, y) in enumerate(cam_obs):
    #     cv2.circle(image, (int(x), int(y) ), 5, (0, 0, 255), -1)
    #     x1, y1 = cam_obs_2[i]
    #     cv2.circle(image, (int(x1), int(y1)), 5, (0, 255, 0), -1)
    #
    cam_obs = np.asarray(cam_obs)

    if True:
        # 侧投 3～5度的点
        cam_obs_2 = get_point(fn='data/cam_720p_yaw.json')
        cam_obs_2 = np.asarray(cam_obs_2)
        diff = np.linalg.norm(cam_obs_2 - cam_obs, axis=1)
        print("diff [pixels]:", diff.mean())
        print("max diff [pixels]:", diff.max())


    # cv2.imshow("Chessboard with Corners", image)
    # cv2.waitKey(0)



    # 初始猜测：D 也给一个大概值就行
    theta0 = np.array([0.0, 0.0, 0.0, 2.0], dtype=np.float64)
    print("Initial guess [pitch,yaw,roll,D]:", theta0)

    # 给 D 加个约束：比如 0.5m ~ 4m
    lower = np.array([-30.0, -30.0, -30.0, 1.98])
    upper = np.array([30.0, 30.0, 30.0, 2.02])

    fixed = SystemFixed(Kp=Kp, Kc=Kc, dx=dx)

    result = least_squares(
        residual_params,
        theta0,
        args=(fixed, proj_pts, cam_obs),
        method="trf",
        bounds=(lower, upper),
        verbose=1
    )

    pitch_est, yaw_est, roll_est, D_est = result.x

    #pitch_est, yaw_est, roll_est, D_est = 12, 1, -1, 2
    print("\nEstimated angles (deg):", pitch_est, yaw_est, roll_est)
    print("Estimated D (m):", D_est)

    # 用估计值看看重投影误差（对无噪声 GT 点）
    Pw_est = projector_pixel_to_world(proj_pts[:, 0], proj_pts[:, 1], Kp, D_est)
    cam_pred_est = world_to_camera_pixel(Pw_est, Kc, dx, D_est,
                                         pitch_est, yaw_est, roll_est)
    reproj_err = np.linalg.norm(cam_pred_est - cam_obs, axis=1)
    print(reproj_err)
    print("Mean reprojection error [pixels]:", reproj_err.mean())
    print("Max  reprojection error [pixels]:", reproj_err.max())

    # for i, (x, y) in enumerate(cam_obs):
    #     cv2.circle(image, (int(x), int(y) ), 5, (0, 0, 255), -1)
    #     x1, y1 = cam_pred_est[i]
    #     cv2.circle(image, (int(x1), int(y1)), 5, (0, 255, 0), -1)
    #     # 显示结果
    #     cv2.imshow("Chessboard with Corners", image)
    #     cv2.waitKey(0)

