"""
坐标系和旋转方向说明
"""
import numpy as np
import math

def rot_y(deg):
    """Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def analyze_coordinate_system():
    print("坐标系和旋转方向分析")
    print("=" * 50)
    
    # 初始设置
    D_m = 1.5  # 投影仪到墙面距离
    dx_cam = 0.1  # camera相对投影仪的X方向距离
    
    print(f"投影仪位置: (0, 0, {-D_m})")
    print(f"墙面位置: Z = 0")
    print(f"Camera相对投影仪偏移: ({dx_cam}, 0, 0)")
    print()
    
    # Camera初始位置（无旋转时）
    proj_pos = np.array([0, 0, -D_m])
    cam_initial_pos = proj_pos + np.array([dx_cam, 0, 0])
    print(f"Camera初始位置: ({cam_initial_pos[0]:.1f}, {cam_initial_pos[1]:.1f}, {cam_initial_pos[2]:.1f})")
    
    # Camera初始朝向（默认朝向+Z方向，即朝向墙面）
    cam_initial_direction = np.array([0, 0, 1])
    print(f"Camera初始朝向: {cam_initial_direction} (朝向墙面)")
    print()
    
    # 分析不同yaw角度的效果
    print("Yaw旋转分析:")
    print("-" * 30)
    
    for yaw in [-2.0, 0.0, 2.0]:
        R_y = rot_y(yaw)
        new_direction = R_y @ cam_initial_direction
        
        print(f"yaw = {yaw:+4.1f}°:")
        print(f"  新朝向: ({new_direction[0]:+6.3f}, {new_direction[1]:+6.3f}, {new_direction[2]:+6.3f})")
        
        # 分析朝向变化
        if yaw > 0:
            print(f"  效果: Camera从朝向墙面转向朝向投影仪方向 (X负方向)")
        elif yaw < 0:
            print(f"  效果: Camera从朝向墙面转向远离投影仪方向 (X正方向)")
        else:
            print(f"  效果: Camera直接朝向墙面")
        print()
    
    print("结论:")
    print("-" * 30)
    print("• 在当前坐标系中，yaw > 0 使camera朝向投影仪方向")
    print("• 因此 yaw_cam = +2.0° 是正确的，表示camera向投影仪方向偏转2°")
    print("• 这符合右手坐标系的Y轴旋转定义")
    
    print("\n坐标系约定:")
    print("-" * 30)
    print("• X轴: 水平方向，+X向右")
    print("• Y轴: 垂直方向，+Y向上") 
    print("• Z轴: 深度方向，+Z向前（朝向墙面）")
    print("• Yaw旋转: 绕Y轴，正角度从+X向+Z旋转（右手定则）")

if __name__ == "__main__":
    analyze_coordinate_system()
