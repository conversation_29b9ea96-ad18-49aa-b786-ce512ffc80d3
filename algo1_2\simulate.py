import math
import numpy as np

def calculate_distance(calib_u1, calib_u2, current_u):
    """
    计算投影仪离墙的距离（米）。
    
    参数:
    calib_u1: 列表，d=1m时所有角点的图像x坐标（来自校准图像）
    calib_u2: 列表，d=1.7m时所有角点的图像x坐标（来自校准图像）
    current_u: 列表，当前图像中对应角点的图像x坐标
    
    返回:
    d_avg: 平均计算的距离（米）
    """
    # 计算常数 N (0.1米 * sin(3°))
    angle_deg = 3
    angle_rad = math.radians(angle_deg)
    sin3 = math.sin(angle_rad)
    N = 0.08 * sin3  # 单位：米
    
    # 计算每个角点的 R_i = (u2 - u1) / (u1 - k * u2)，其中 k = d2/d1 = 1.7
    R_list = []
    for i in range(len(calib_u1)):
        u1 = calib_u1[i]
        u2 = calib_u2[i]
        # 关键修改：将分母中的 "2" 替换为 "1.7"（因为 d2=1.7m）
        k = 1.7  # d2/d1 = 1.7 / 1
        if abs(u1 - k * u2) < 1e-5:
            raise ValueError(f"Point {i}: Denominator near zero (u1={u1}, u2={u2}). Check calibration data.")
        R_i = (u2 - u1) / (u1 - k * u2)  # 修改点：此处的 "k" 已设为 1.7
        R_list.append(R_i)
    
    R_avg = np.mean(R_list)
    M = R_avg * N
    
    # 计算每个点的距离 d_calc
    d_list = []
    for i in range(len(current_u)):
        u1_cal = calib_u1[i]
        u_curr = current_u[i]
        numerator = (M + N) * (u1_cal / u_curr) - N
        if abs(M) < 1e-5:
            raise ValueError("M is near zero. Check calibration data.")
        d_calc = numerator / M
        d_list.append(d_calc)
    
    d_avg = np.mean(d_list)
    return d_avg

# 示例使用
if __name__ == "__main__":
    print(np.arctan(632 / 1000) * 180 / np.pi)
    print(np.tan(math.radians(34)))
    print(1.25/0.67, 1920/1080 * 0.67, np.arctan(0.625) * 2 * 180 / np.pi, np.arctan(1920/1080 * 0.67 / 2) * 2 * 180 / np.pi)
    print(0.097 / np.tan(math.radians(2.1)) )
    print(np.arctan((56+54*9)/(1080/np.tan(math.radians(34))))*180/np.pi)
    print(0.097 * 793 / 1.4 )
    print(np.arctan((619 - 606) / (1098 - 187)) * 180 / np.pi)

    # d = 1.0
    # c = d / np.cos(math.radians(18.7))
    # theta = np.arctan(0.097 / c)
    ds = np.arange(10, 29, 1) * 0.1
    thetas = 90 - 2.1 - np.arctan(ds / 0.097) * 180 / np.pi
    thetas = [np.tan(math.radians(theta)) for theta in thetas]
    print(ds)
    print(thetas)
    quit()
    # 校准数据 (d=1m 和 d=2m 时的角点x坐标)
    # 假设有5个角点，实际中需从棋盘格检测获取
    calib_u1 = [186.0, 1078.0, 1155.0, 90.0, 607.0]  # d=1m时的x坐标
    calib_u2 = [219.0, 1107.0, 1190.0, 132.0, 642.0]  # d=2m时的x坐标
    
    # 当前图像数据 (任意距离d)
    current_u = [216.0, 1104.0, 1187.0, 128.0, 640.0]  # 当前图像的x坐标
    
    # 计算距离
    d = calculate_distance(calib_u1, calib_u2, current_u)
    print(f"Estimated distance from wall: {d:.2f} meters")