"""
测试修正后的camera-投影仪几何关系
"""
import numpy as np
import math

def rot_y(deg):
    """Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def test_corrected_geometry():
    print("测试修正后的Camera-投影仪几何关系")
    print("=" * 50)
    
    # 参数设置（与代码中一致）
    D_m = 1.5
    dx_cam = 0.1
    pitch_cam, yaw_cam, roll_cam = 17.0, -2.0, 0.0  # 修正后的值
    pitch_proj, yaw_proj, roll_proj = 0.0, 0.0, 0.0
    
    print("修正后的参数:")
    print(f"• 投影仪位置: (0, 0, {-D_m})")
    print(f"• Camera位置: ({dx_cam}, 0, {-D_m})")
    print(f"• Camera yaw: {yaw_cam}° (负值表示朝向投影仪方向)")
    print()
    
    # 计算camera朝向
    R_cam = rot_y(yaw_cam)
    initial_direction = np.array([0, 0, 1])  # +Z方向
    cam_direction = R_cam @ initial_direction
    
    print("Camera朝向分析:")
    print(f"• 初始朝向: {initial_direction} (+Z方向，朝向墙面)")
    print(f"• yaw = {yaw_cam}°后: ({cam_direction[0]:+6.3f}, {cam_direction[1]:+6.3f}, {cam_direction[2]:+6.3f})")
    
    # 计算与投影仪方向的夹角
    proj_direction = np.array([-1, 0, 0])  # 投影仪在-X方向
    dot_product = np.dot(cam_direction, proj_direction)
    angle_to_proj = math.degrees(math.acos(np.clip(dot_product, -1, 1)))
    
    print(f"• 与投影仪方向夹角: {angle_to_proj:.1f}°")
    
    if angle_to_proj < 90:
        print("✓ Camera确实朝向投影仪方向偏转")
    else:
        print("✗ Camera背离投影仪方向")
    
    print()
    
    # 对比修正前后
    print("修正前后对比:")
    print("-" * 30)
    
    # 修正前 yaw = +2.0
    R_old = rot_y(2.0)
    old_direction = R_old @ initial_direction
    old_angle = math.degrees(math.acos(np.clip(np.dot(old_direction, proj_direction), -1, 1)))
    
    print(f"修正前 yaw = +2.0°:")
    print(f"  朝向: ({old_direction[0]:+6.3f}, {old_direction[1]:+6.3f}, {old_direction[2]:+6.3f})")
    print(f"  与投影仪夹角: {old_angle:.1f}° (>90°表示背离)")
    
    print(f"修正后 yaw = -2.0°:")
    print(f"  朝向: ({cam_direction[0]:+6.3f}, {cam_direction[1]:+6.3f}, {cam_direction[2]:+6.3f})")
    print(f"  与投影仪夹角: {angle_to_proj:.1f}° (<90°表示朝向)")
    
    print()
    print("结论:")
    print("-" * 30)
    print("✓ 修正后的 yaw = -2.0° 确实表示camera朝向投影仪方向")
    print("✓ 符合几何关系：投影仪在camera左侧(-X方向)")
    print("✓ 负的yaw值表示从+Z向-X方向偏转")
    
    print()
    print("键盘控制说明:")
    print("-" * 30)
    print("• F键 (yaw减少): camera更朝向投影仪")
    print("• H键 (yaw增加): camera背离投影仪")
    print("• 当前设置 yaw = -2° 是朝向投影仪的小角度偏转")

if __name__ == "__main__":
    test_corrected_geometry()
