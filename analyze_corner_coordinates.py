"""
分析camera拍到的棋盘格图像中角点坐标的问题
"""
import numpy as np
import math

def rot_y(deg):
    """Y轴旋转矩阵"""
    a = math.radians(deg)
    ca, sa = math.cos(a), math.sin(a)
    return np.array([[ca,0,sa],[0,1,0],[-sa,0,ca]], dtype=np.float64)

def build_rotation(pitch, yaw, roll):
    """构建旋转矩阵"""
    def rot_x(deg):
        a = math.radians(deg)
        ca, sa = math.cos(a), math.sin(a)
        return np.array([[1,0,0],[0,ca,-sa],[0,sa,ca]], dtype=np.float64)
    
    def rot_z(deg):
        a = math.radians(deg)
        ca, sa = math.cos(a), math.sin(a)
        return np.array([[ca,-sa,0],[sa,ca,0],[0,0,1]], dtype=np.float64)
    
    return rot_z(roll) @ rot_y(yaw) @ rot_x(pitch)

def analyze_corner_coordinates():
    print("Camera拍到的棋盘格图像角点坐标分析")
    print("=" * 60)
    
    # 模拟参数
    W, H = 1920, 1080  # 投影仪分辨率
    D_m = 1.5          # 投影仪到墙面距离
    dx_cam = 0.1       # camera相对投影仪距离
    
    # 投影仪和camera的姿态
    pitch_proj, yaw_proj, roll_proj = 0.0, 0.0, 0.0
    pitch_cam, yaw_cam, roll_cam = 17.0, -2.0, 0.0
    
    print("坐标系定义:")
    print("• 世界坐标系: X向右, Y向上, Z向前(朝向墙面)")
    print("• 图像坐标系: u向右, v向下, 原点在左上角")
    print("• 墙面: Z = 0平面")
    print()
    
    # 投影仪内参（简化）
    throw_ratio = 1.2
    fovx = 2.0 * math.atan(1.0 / (2.0 * throw_ratio))
    fx = (W / 2.0) / math.tan(fovx / 2.0)
    fy = fx
    cx, cy = W / 2.0, H / 2.0
    K = np.array([[fx,0,cx],[0,fy,cy],[0,0,1]], dtype=np.float64)
    
    print("投影仪四个角点在像素坐标系中:")
    corners_pixel = [
        [0, 0],        # 左上角
        [W-1, 0],      # 右上角  
        [W-1, H-1],    # 右下角
        [0, H-1]       # 左下角
    ]
    
    for i, (u, v) in enumerate(corners_pixel):
        corner_names = ["左上", "右上", "右下", "左下"]
        print(f"• {corner_names[i]}: ({u:4d}, {v:4d})")
    print()
    
    # 计算这些角点在墙面上的世界坐标
    print("投影仪角点在墙面上的世界坐标:")
    R_proj = build_rotation(pitch_proj, yaw_proj, roll_proj)
    Kinv = np.linalg.inv(K)
    
    world_corners = []
    for i, (u, v) in enumerate(corners_pixel):
        # 像素坐标转换为射线方向
        pixel_homo = np.array([u, v, 1.0])
        ray_dir = Kinv @ pixel_homo
        ray_world = R_proj @ ray_dir
        
        # 与Z=0平面的交点
        if abs(ray_world[2]) > 1e-6:
            t = D_m / ray_world[2]
            world_point = np.array([0, 0, -D_m]) + ray_world * t
            world_corners.append([world_point[0], world_point[1]])
            
            corner_names = ["左上", "右上", "右下", "左下"]
            print(f"• {corner_names[i]}: X={world_point[0]:7.3f}, Y={world_point[1]:7.3f}")
    
    print()
    print("关键观察:")
    print("-" * 40)
    print(f"• 左上角世界坐标Y: {world_corners[0][1]:7.3f}")
    print(f"• 右上角世界坐标Y: {world_corners[1][1]:7.3f}")
    print(f"• 右上角Y值 - 左上角Y值 = {world_corners[1][1] - world_corners[0][1]:7.3f}")
    
    if world_corners[1][1] < world_corners[0][1]:
        print("✓ 确实，右上角的Y值比左上角的Y值要小！")
    
    print()
    print("原因分析:")
    print("-" * 40)
    print("1. 投影仪的透视投影特性:")
    print("   • 投影仪位于 (0, 0, -1.5)，朝向墙面")
    print("   • 投影仪的光轴指向墙面中心")
    print("   • 由于透视投影，图像会产生梯形失真")
    
    print()
    print("2. 几何关系:")
    print("   • 投影仪在墙面下方，向上投影")
    print("   • 图像上方的像素对应墙面上更远的位置")
    print("   • 图像右侧的像素由于透视效果，在墙面上的Y坐标会略低")
    
    print()
    print("3. 透视效应:")
    print("   • 这是正常的透视投影现象")
    print("   • 类似于拍照时的'梯形失真'")
    print("   • 投影仪不是正交投影，而是透视投影")
    
    print()
    print("验证方法:")
    print("-" * 40)
    print("• 如果投影仪完全水平放置且正对墙面中心")
    print("• 左上角和右上角的Y值应该相等")
    print("• 当前的差异可能来自:")
    print("  - 投影仪的轻微倾斜")
    print("  - 透视投影的固有特性")
    print("  - 数值计算的精度")

if __name__ == "__main__":
    analyze_corner_coordinates()
