import os
import cv2
import numpy as np


def get_corner(img, corner_init):
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    corner_init = np.array(corner_init, dtype=np.float32)
    corners_subpix = cv2.cornerSubPix(
            gray, corner_init, (7, 7), (-1, -1),
            criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
        )
    
    return corners_subpix


def main():
    sirdir = r"D:\Dataset\keystone"

    point_dict = {"cam_100cm_0d": [(573, 345)], 
                  "cam_110cm_0d": [(580, 345)],
                  "cam_120cm_0d": [(586, 346)],
                  "cam_130cm_0d": [(592, 346)],
                  "cam_140cm_0d": [(596, 346)],
                  "cam_150cm_0d": [(600, 346)],
                  "cam_160cm_0d": [(603, 347)],
                  "cam_170cm_0d": [(606, 347)],
                  "cam_180cm_0d": [(609, 347)],
                  "cam_190cm_0d": [(612, 347)],
                  "cam_200cm_0d": [(614, 347)]}
    
    for img_name in point_dict.keys():
        image = cv2.imread(os.path.join(sirdir, img_name + ".jpg"))
        corner = get_corner(image, point_dict[img_name])
        print(img_name, corner)

    quit()

    img_dict = {
        "cam_1080p_100cm_0_no": [(186, 109), (1078,126), (1155, 650), (90, 636), (612, 142), (607, 357), (604, 611)],
        "cam_1080p_110cm_0_no": [(193, 110), (1085,127), (1163, 650), (100, 636), (619, 142), (616, 358), (612, 611)],
        "cam_1080p_120cm_0_no": [(200, 110), (1090,127), (1168, 648), (108, 636), (626, 142), (622, 358), (618, 613)],
        "cam_1080p_130cm_0_no": [(205, 111), (1094,127), (1174, 649), (115, 636), (630, 143), (628, 358), (625, 612)],
        "cam_1080p_140cm_0_no": [(211, 110), (1099,128), (1179, 649), (119, 636), (634, 143), (632, 359), (629, 612)],
        "cam_1080p_150cm_0_no": [(213, 111), (1101,129), (1183, 650), (124, 636), (639, 144), (636, 359), (633, 613)],
        "cam_1080p_160cm_0_no": [(216, 111), (1104,129), (1187, 650), (128, 637), (642, 144), (640, 359), (638, 614)],
        "cam_1080p_170cm_0_no": [(219, 112), (1107,129), (1190, 650), (132, 637), (644, 143), (642, 359), (641, 614)],

        "cam_1080p_150cm_5_no": [(217, 111), (1097,128), (1179, 648), (130, 634), (638, 142), (635, 358), (634, 611)],
        "cam_1080p_150cm_10_no": [(217, 110), (1096,126), (1176, 649), (130, 636), (638, 143), (635, 357), (634, 612)],
        "cam_1080p_150cm_15_no": [(220, 110), (1093,126), (1175, 650), (134, 636), (640, 141), (636, 359), (635, 612)],
        "cam_1080p_150cm_20_no": [(223, 110), (1093,126), (1174, 650), (137, 637), (641, 141), (638, 357), (635, 611)],

        "cam_1080p_150cm_5_r_no": [(212, 110), (1105,128), (1188, 650), (123, 636), (640, 143), (638, 359), (634, 613)],
        "cam_1080p_150cm_10_r_no": [(211, 111), (1108,128), (1191, 650), (121, 636), (640, 143), (638, 358), (636, 613)],
        "cam_1080p_150cm_15_r_no": [(209, 110), (1111,129), (1194, 649), (119, 637), (642, 143), (639, 359), (637, 612)],
        "cam_1080p_150cm_20_r_no": [(208, 110), (1114,129), (1197, 649), (119, 637), (643, 143), (641, 359), (638, 613)]
    }

    img_dict_new = {}

    xmap = np.frombuffer(open(r"D:\xmap.bin", "rb").read(), dtype=np.float32)
    ymap = np.frombuffer(open(r"D:\ymap.bin", "rb").read(), dtype=np.float32)
    xmap = xmap.reshape((720, 1280))
    ymap = ymap.reshape((720, 1280))

    for img_name in img_dict.keys():
        image = cv2.imread(os.path.join(sirdir, img_name + ".jpg"))
        corner = get_corner(image, img_dict[img_name])

        distortimg = cv2.remap(image, xmap, ymap, cv2.INTER_LINEAR)
        cv2.imwrite(r"D:\Dataset\keystone_distort\{}_distort.jpg".format(img_name), distortimg)

        img_dict_new[img_name] = corner

        for corner in corner:
            # 将浮点坐标转换为整数像素位置，注意OpenCV绘图函数需要整数坐标
            x, y = corner.ravel()
            center = (int(round(x)), int(round(y))) # 核心：坐标取整
            # 用绿色实心圆标记角点，半径5像素
            cv2.circle(image, center, 5, (0, 255, 0), -1) 
        cv2.imshow("image", image)
        cv2.waitKey(20)

    print(img_dict_new)

if __name__ == "__main__":
    main()