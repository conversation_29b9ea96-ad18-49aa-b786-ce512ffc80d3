import cv2
import numpy as np

import cv2
import numpy as np
import yaml
import os

# -------------------------- 1. 加载相机内参 --------------------------
def load_camera_params(params_path):
    with open(params_path, "r") as f:
        params = yaml.safe_load(f)
    return (
        np.array(params["camera_matrix"]),
        np.array(params["dist_coeffs"]),
        tuple(params["image_size"])
    )

def calculate_relative_pose(camera_image_path, pattern_size=(9, 6), cell_size=100.0):
    """
    计算摄像头与光机的相对角度和偏移。
    
    参数:
    camera_image_path (str): 摄像头拍摄的棋盘格图像路径
    pattern_size (tuple): 棋盘格的格子数 (cols, rows), 例如 (9, 6) 表示9列6行格子
    cell_size (float): 棋盘格每个单元格的大小 (像素), 用于生成光机坐标系点
    
    返回:
    dict: 包含 'homography' (单应性矩阵), 'rotation_angle' (相对旋转角, 弧度), 
          'translation' (相对偏移 [dx, dy], 像素)
    """
    # 1. 生成光机坐标系的棋盘格角点 (理想矩形, 无畸变)
    # 角点数量: (cols+1) x (rows+1)
    # 注意：solvePnP需要3D点，所以添加Z=0坐标
    object_points = []
    for i in range(pattern_size[0] + 1):
        for j in range(pattern_size[1] + 1):
            object_points.append([i * cell_size, j * cell_size, 0.0])  # 添加Z=0
    object_points = np.array(object_points, dtype=np.float32)  # [N, 3]

    # 2. 读取摄像头图像并检测角点
    img = cv2.imread(camera_image_path)
    if img is None:
        raise ValueError(f"图像文件 {camera_image_path} 不存在或无法读取")
    
    # 检测棋盘格角点 (返回角点坐标)
    # ret, corners = cv2.findChessboardCorners(img, pattern_size, None)
    # if not ret:
    #     raise ValueError("未检测到棋盘格角点，请检查图像或调整检测参数")
    
    # 调整角点顺序 (确保与object_points匹配)
    corners = np.array([[502, 431], [551, 430], [604, 430], [655, 429], [707, 429], [758, 428], [500, 481], 
                        [552, 481], [603, 481], [655, 480], [709, 480], [761, 480], [497, 533], [550, 534], 
                        [603, 533], [656, 533], [709, 533], [764, 533], [494, 589], [548, 588], [602, 588], 
                        [658, 588], [711, 589], [766, 587]], dtype=np.float32)
    corners = corners.reshape(-1, 2)
    
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    corners = cv2.cornerSubPix(
            gray, corners, (11, 11), (-1, -1),
            criteria=(cv2.TERM_CRITERIA_EPS + cv2.TERM_CRITERIA_MAX_ITER, 30, 0.001)
        )

    # 3. 验证点数量
    if len(object_points) != len(corners):
        raise ValueError(f"3D点数量({len(object_points)})与2D点数量({len(corners)})不匹配")

    if len(corners) < 4:
        raise ValueError(f"点数量不足：需要至少4个点，但只有{len(corners)}个点")

    # 4. 加载相机参数并求解PnP
    camera_matrix, dist_coeffs, img_size = load_camera_params("../algo1_2/camera_params.yml")
    #dist_coeffs = np.zeros(5, dtype=np.float32)

    # 确保corners是正确的形状 [N, 2]
    corners = corners.reshape(-1, 2)
    print(corners, object_points)
    try:
        success, rvec, tvec = cv2.solvePnP(object_points, corners, camera_matrix, None)
        print(rvec, tvec)
        quit()
    except cv2.error as e:
        raise ValueError(f"solvePnP失败: {str(e)}. 请检查输入点的格式和数量。")
    if success:
        # 可选：将旋转向量转换为旋转矩阵
        R, _ = cv2.Rodrigues(rvec)

        print("旋转矩阵 R:\n", R)
        print("平移向量 t:", tvec.flatten())

        # 可选：计算重投影误差以评估精度
        projected_points, _ = cv2.projectPoints(object_points, rvec, tvec, camera_matrix, dist_coeffs)
        projected_points = projected_points.reshape(-1, 2)  # 确保形状正确
        error = cv2.norm(corners, projected_points, cv2.NORM_L2) / len(projected_points)
        print(f"平均重投影误差 (像素): {error}")

        # 计算旋转角度（绕Z轴的旋转）
        rotation_angle = np.arctan2(R[1, 0], R[0, 0])

        # 返回结果字典
        return {
            'rotation_matrix': R,
            'rotation_vector': rvec.flatten(),
            'translation': tvec.flatten(),
            'rotation_angle': rotation_angle,
            'reprojection_error': error,
            'success': True
        }
    else:
        print("solvePnP求解失败")
        return {
            'success': False,
            'error': 'solvePnP求解失败'
        }


# 示例用法
if __name__ == "__main__":
    # 替换为您的摄像头图像路径
    camera_image_path = r"D:\Dataset\keystone\cam_1.jpg"
    
    # 计算相对姿态
    result = calculate_relative_pose(
        camera_image_path,
        pattern_size=(5, 3),  # 棋盘格格子数 (5列3行)
        cell_size=0.06      # 棋盘格单元格大小 (像素)
    )

    # 打印结果
    if result['success']:
        print("\n=== 计算结果 ===")
        print(f"旋转角度 (弧度): {result['rotation_angle']:.6f}")
        print(f"旋转角度 (度): {np.degrees(result['rotation_angle']):.2f}")
        print(f"重投影误差: {result['reprojection_error']:.2f} 像素")
        print("计算成功!")
    else:
        print(f"计算失败: {result['error']}")
